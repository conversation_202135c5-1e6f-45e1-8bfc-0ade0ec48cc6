{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\System Development Project\\\\pri new\\\\frontend\\\\src\\\\pages\\\\AddCutting.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Select from 'react-select';\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\nimport { Card, Form, Button, Row, Col, Spinner, Alert, Badge, Modal } from 'react-bootstrap';\nimport { BsScissors, BsPlus, BsTrash, BsCheck2Circle, BsExclamationTriangle, BsFilePdf } from 'react-icons/bs';\nimport jsPDF from 'jspdf';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddCuttingRecord = () => {\n  _s();\n  // Overall cutting record fields\n  const [fabricDefinitions, setFabricDefinitions] = useState([]);\n  const [allFabricVariants, setAllFabricVariants] = useState([]);\n  const [cuttingDate, setCuttingDate] = useState('');\n  const [description, setDescription] = useState('');\n  const [productName, setProductName] = useState('');\n\n  // Fabric definition groups - each group has a fabric definition and its variants\n  const [fabricGroups, setFabricGroups] = useState([{\n    id: Date.now(),\n    fabric_definition: '',\n    variants: [{\n      fabric_variant: '',\n      yard_usage: '',\n      xs: 0,\n      s: 0,\n      m: 0,\n      l: 0,\n      xl: 0\n    }]\n  }]);\n\n  // Loading, error, success states\n  const [loadingVariants, setLoadingVariants] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\n  const [validated, setValidated] = useState(false);\n  const [showPdfModal, setShowPdfModal] = useState(false);\n  const [submittedRecord, setSubmittedRecord] = useState(null);\n\n  // Add resize event listener to update sidebar state\n  useEffect(() => {\n    const handleResize = () => {\n      setIsSidebarOpen(window.innerWidth >= 768);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n\n  // Fetch all fabric variants on mount\n  useEffect(() => {\n    setLoadingVariants(true);\n    axios.get(\"http://localhost:8000/api/fabric-variants/\").then(res => {\n      setAllFabricVariants(res.data);\n      setLoadingVariants(false);\n    }).catch(err => {\n      console.error('Error fetching fabric variants:', err);\n      setError('Failed to load fabric variants. Please try again.');\n      setLoadingVariants(false);\n    });\n  }, []);\n\n  // Add a new empty detail row\n  const addDetailRow = () => {\n    setDetails([...details, {\n      fabric_variant: '',\n      yard_usage: '',\n      xs: 0,\n      s: 0,\n      m: 0,\n      l: 0,\n      xl: 0\n    }]);\n  };\n\n  // Delete a detail row\n  const removeDetailRow = index => {\n    const newDetails = details.filter((_, i) => i !== index);\n    setDetails(newDetails);\n  };\n\n  // Check if a fabric variant is already selected in another detail row\n  const isDuplicateFabricVariant = (variantId, currentIndex) => {\n    return details.some((detail, idx) => idx !== currentIndex && detail.fabric_variant === variantId && variantId !== '');\n  };\n\n  // Handle change for each detail row field\n  const handleDetailChange = (index, field, value) => {\n    const newDetails = [...details];\n\n    // If changing fabric variant, check for duplicates\n    if (field === 'fabric_variant') {\n      if (isDuplicateFabricVariant(value, index)) {\n        setError(`This fabric variant is already selected in another detail. Please select a different variant.`);\n        return; // Don't update the state if duplicate\n      } else {\n        setError(''); // Clear error if no duplicate\n      }\n    }\n    newDetails[index][field] = value;\n    setDetails(newDetails);\n  };\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Form validation\n    const form = e.currentTarget;\n    if (form.checkValidity() === false) {\n      e.stopPropagation();\n      setValidated(true);\n      return;\n    }\n\n    // Check if any detail has a fabric variant selected\n    const hasValidDetails = details.some(detail => detail.fabric_variant);\n    if (!hasValidDetails) {\n      setError('Please select at least one fabric variant for your cutting details.');\n      return;\n    }\n\n    // Check for duplicate fabric variants\n    const selectedVariants = details.map(detail => detail.fabric_variant).filter(Boolean);\n    const uniqueVariants = [...new Set(selectedVariants)];\n    if (selectedVariants.length !== uniqueVariants.length) {\n      setError('You have selected the same fabric variant in multiple details. Please use unique fabric variants for each detail.');\n      setValidated(true);\n      return;\n    }\n\n    // Validate yard availability for each detail\n    let yardValidationError = false;\n    details.forEach(detail => {\n      if (detail.fabric_variant) {\n        const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\n        if (variant && parseFloat(detail.yard_usage) > (variant.available_yard || variant.total_yard)) {\n          yardValidationError = true;\n          setError(`Yard usage for ${variant.color_name || variant.color} exceeds available yards (${variant.available_yard || variant.total_yard} yards available).`);\n        }\n      }\n    });\n    if (yardValidationError) {\n      setValidated(true);\n      return;\n    }\n    setValidated(true);\n    setIsSubmitting(true);\n    setError('');\n    setSuccess('');\n    const payload = {\n      cutting_date: cuttingDate,\n      description: description,\n      product_name: productName,\n      details: details\n    };\n    try {\n      const response = await axios.post(\"http://localhost:8000/api/cutting/cutting-records/\", payload);\n      setSuccess('Cutting record created successfully!');\n\n      // Store the submitted record for PDF generation\n      const fabricNames = new Set();\n      const recordData = {\n        ...response.data,\n        details: response.data.details.map(detail => {\n          var _variant$fabric_defin, _variant$fabric_defin2;\n          const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\n          if (variant !== null && variant !== void 0 && (_variant$fabric_defin = variant.fabric_definition_data) !== null && _variant$fabric_defin !== void 0 && _variant$fabric_defin.fabric_name) {\n            fabricNames.add(variant.fabric_definition_data.fabric_name);\n          }\n          return {\n            ...detail,\n            color: (variant === null || variant === void 0 ? void 0 : variant.color) || 'Unknown',\n            color_name: (variant === null || variant === void 0 ? void 0 : variant.color_name) || (variant === null || variant === void 0 ? void 0 : variant.color) || 'Unknown',\n            fabric_name: (variant === null || variant === void 0 ? void 0 : (_variant$fabric_defin2 = variant.fabric_definition_data) === null || _variant$fabric_defin2 === void 0 ? void 0 : _variant$fabric_defin2.fabric_name) || 'Unknown'\n          };\n        }),\n        fabric_names: Array.from(fabricNames).join(', ') || 'Unknown',\n        totalQuantities: totalQuantities\n      };\n      setSubmittedRecord(recordData);\n\n      // Show the PDF generation modal\n      setShowPdfModal(true);\n    } catch (err) {\n      console.error('Error creating cutting record:', err);\n      if (err.response && err.response.data) {\n        // Display more specific error message if available\n        const errorMessage = typeof err.response.data === 'string' ? err.response.data : 'Failed to create cutting record. Please check your inputs.';\n        setError(errorMessage);\n      } else {\n        setError('Failed to create cutting record. Please try again.');\n      }\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Function to generate PDF directly without using html2canvas\n  const generatePDF = () => {\n    if (!submittedRecord) return;\n    try {\n      // Create a new PDF document\n      const pdf = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n      });\n\n      // Set font sizes and styles\n      const titleFontSize = 18;\n      const headingFontSize = 14;\n      const normalFontSize = 10;\n      const smallFontSize = 8;\n\n      // Add title\n      pdf.setFontSize(titleFontSize);\n      pdf.setFont('helvetica', 'bold');\n      pdf.text('Cutting Record', 105, 20, {\n        align: 'center'\n      });\n\n      // Add general information section\n      pdf.setFontSize(headingFontSize);\n      pdf.text('General Information', 20, 35);\n      pdf.setFontSize(normalFontSize);\n      pdf.setFont('helvetica', 'normal');\n\n      // Draw table for general info\n      pdf.line(20, 40, 190, 40); // Top horizontal line\n\n      const generalInfoData = [['Record ID', submittedRecord.id.toString()], ['Product Name', submittedRecord.product_name], ['Fabrics Used', submittedRecord.fabric_names], ['Cutting Date', new Date(submittedRecord.cutting_date).toLocaleDateString()], ['Description', submittedRecord.description || 'N/A']];\n      let yPos = 45;\n      generalInfoData.forEach(row => {\n        pdf.setFont('helvetica', 'bold');\n        pdf.text(row[0], 25, yPos);\n        pdf.setFont('helvetica', 'normal');\n        pdf.text(row[1], 80, yPos);\n        yPos += 8;\n        pdf.line(20, yPos - 3, 190, yPos - 3); // Horizontal line after each row\n      });\n\n      // Add fabric details section\n      pdf.setFontSize(headingFontSize);\n      pdf.setFont('helvetica', 'bold');\n      pdf.text('Fabric Details', 20, yPos + 10);\n\n      // Table headers for fabric details\n      const headers = ['Fabric', 'Color', 'Yard Usage', 'XS', 'S', 'M', 'L', 'XL', 'Total'];\n      const colWidths = [35, 35, 20, 12, 12, 12, 12, 12, 15];\n\n      // Calculate starting positions for each column\n      const colPositions = [];\n      let currentPos = 20;\n      colWidths.forEach(width => {\n        colPositions.push(currentPos);\n        currentPos += width;\n      });\n\n      // Draw table header\n      yPos += 15;\n      pdf.setFontSize(normalFontSize);\n      pdf.setFont('helvetica', 'bold');\n\n      // Draw header background\n      pdf.setFillColor(240, 240, 240);\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\n\n      // Draw header text\n      headers.forEach((header, index) => {\n        pdf.text(header, colPositions[index] + 2, yPos);\n      });\n\n      // Draw horizontal line after header\n      yPos += 3;\n      pdf.line(20, yPos, 190, yPos);\n\n      // Draw table rows\n      pdf.setFont('helvetica', 'normal');\n      submittedRecord.details.forEach(detail => {\n        var _detail$xs, _detail$s, _detail$m, _detail$l, _detail$xl;\n        yPos += 8;\n\n        // Calculate total for this row\n        const total = parseInt(detail.xs || 0) + parseInt(detail.s || 0) + parseInt(detail.m || 0) + parseInt(detail.l || 0) + parseInt(detail.xl || 0);\n\n        // Draw row data\n        pdf.text(detail.fabric_name || 'Unknown', colPositions[0] + 2, yPos);\n        pdf.text(detail.color_name || detail.color, colPositions[1] + 2, yPos);\n        pdf.text(`${detail.yard_usage} yards`, colPositions[2] + 2, yPos);\n        pdf.text(((_detail$xs = detail.xs) === null || _detail$xs === void 0 ? void 0 : _detail$xs.toString()) || '0', colPositions[3] + 2, yPos);\n        pdf.text(((_detail$s = detail.s) === null || _detail$s === void 0 ? void 0 : _detail$s.toString()) || '0', colPositions[4] + 2, yPos);\n        pdf.text(((_detail$m = detail.m) === null || _detail$m === void 0 ? void 0 : _detail$m.toString()) || '0', colPositions[5] + 2, yPos);\n        pdf.text(((_detail$l = detail.l) === null || _detail$l === void 0 ? void 0 : _detail$l.toString()) || '0', colPositions[6] + 2, yPos);\n        pdf.text(((_detail$xl = detail.xl) === null || _detail$xl === void 0 ? void 0 : _detail$xl.toString()) || '0', colPositions[7] + 2, yPos);\n        pdf.text(total.toString(), colPositions[8] + 2, yPos);\n\n        // Draw horizontal line after row\n        yPos += 3;\n        pdf.line(20, yPos, 190, yPos);\n      });\n\n      // Draw totals row\n      yPos += 8;\n      pdf.setFillColor(240, 240, 240);\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\n      pdf.setFont('helvetica', 'bold');\n      pdf.text('Total', colPositions[0] + 2, yPos);\n      pdf.text('', colPositions[1] + 2, yPos);\n      pdf.text(`${submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards`, colPositions[2] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.xs.toString(), colPositions[3] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.s.toString(), colPositions[4] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.m.toString(), colPositions[5] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.l.toString(), colPositions[6] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.xl.toString(), colPositions[7] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.total.toString(), colPositions[8] + 2, yPos);\n\n      // Add footer\n      pdf.setFontSize(smallFontSize);\n      pdf.setFont('helvetica', 'italic');\n      pdf.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, {\n        align: 'center'\n      });\n      pdf.text('Fashion Garment Management System', 105, 285, {\n        align: 'center'\n      });\n\n      // Save the PDF\n      pdf.save(`Cutting_Record_${submittedRecord.id}_${submittedRecord.product_name}.pdf`);\n\n      // Reset form after PDF generation\n      setShowPdfModal(false);\n      setCuttingDate('');\n      setDescription('');\n      setProductName('');\n      setDetails([{\n        fabric_variant: '',\n        yard_usage: '',\n        xs: 0,\n        s: 0,\n        m: 0,\n        l: 0,\n        xl: 0\n      }]);\n      setValidated(false);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      setError('Failed to generate PDF. Please try again.');\n      setShowPdfModal(false);\n    }\n  };\n\n  // Function to handle modal close without generating PDF\n  const handleCloseModal = () => {\n    setShowPdfModal(false);\n    // Reset form\n    setCuttingDate('');\n    setDescription('');\n    setProductName('');\n    setDetails([{\n      fabric_variant: '',\n      yard_usage: '',\n      xs: 0,\n      s: 0,\n      m: 0,\n      l: 0,\n      xl: 0\n    }]);\n    setValidated(false);\n  };\n\n  // Custom option component that shows a color swatch + label\n  const ColourOption = ({\n    data,\n    innerRef,\n    innerProps\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: innerRef,\n    ...innerProps,\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      padding: '4px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: 16,\n        height: 16,\n        backgroundColor: data.color,\n        marginRight: 8,\n        border: '1px solid #ccc'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 370,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: data.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 365,\n    columnNumber: 5\n  }, this);\n\n  // Calculate total quantities for all details\n  const totalQuantities = details.reduce((acc, detail) => {\n    acc.xs += parseInt(detail.xs) || 0;\n    acc.s += parseInt(detail.s) || 0;\n    acc.m += parseInt(detail.m) || 0;\n    acc.l += parseInt(detail.l) || 0;\n    acc.xl += parseInt(detail.xl) || 0;\n    acc.total += (parseInt(detail.xs) || 0) + (parseInt(detail.s) || 0) + (parseInt(detail.m) || 0) + (parseInt(detail.l) || 0) + (parseInt(detail.xl) || 0);\n    acc.yard_usage += parseFloat(detail.yard_usage) || 0;\n    return acc;\n  }, {\n    xs: 0,\n    s: 0,\n    m: 0,\n    l: 0,\n    xl: 0,\n    total: 0,\n    yard_usage: 0\n  });\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\n        width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\n        transition: \"all 0.3s ease\",\n        padding: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(BsScissors, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), \"Add Cutting Record\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"success\",\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(BsCheck2Circle, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 13\n        }, this), success]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(BsExclamationTriangle, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 13\n        }, this), error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"mb-4 shadow-sm\",\n        style: {\n          backgroundColor: \"#D9EDFB\",\n          borderRadius: \"10px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            noValidate: true,\n            validated: validated,\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              children: /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Product Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 438,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: productName,\n                    onChange: e => setProductName(e.target.value),\n                    placeholder: \"Enter product name\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                    type: \"invalid\",\n                    children: \"Please provide a product name.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Cutting Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: cuttingDate,\n                    onChange: e => setCuttingDate(e.target.value),\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                    type: \"invalid\",\n                    children: \"Please select a cutting date.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 470,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    as: \"textarea\",\n                    rows: 3,\n                    value: description,\n                    onChange: e => setDescription(e.target.value),\n                    placeholder: \"Enter details about this cutting record...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-0\",\n                children: \"Fabric Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                size: \"sm\",\n                onClick: addDetailRow,\n                disabled: isSubmitting,\n                children: [/*#__PURE__*/_jsxDEV(BsPlus, {\n                  className: \"me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 19\n                }, this), \" Add Fabric Variant\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this), details.map((detail, index) => {\n              var _currentVariant$fabri, _currentVariant$fabri2;\n              // Find the selected variant object to set the value in React-Select\n              const currentVariant = allFabricVariants.find(v => v.id === detail.fabric_variant);\n              const currentValue = currentVariant ? {\n                value: currentVariant.id,\n                label: `${((_currentVariant$fabri = currentVariant.fabric_definition_data) === null || _currentVariant$fabri === void 0 ? void 0 : _currentVariant$fabri.fabric_name) || 'Unknown'} - ${currentVariant.color_name || currentVariant.color} (${currentVariant.available_yard || currentVariant.total_yard} yards available)`,\n                color: currentVariant.color,\n                available_yard: currentVariant.available_yard || currentVariant.total_yard,\n                total_yard: currentVariant.total_yard,\n                fabric_name: ((_currentVariant$fabri2 = currentVariant.fabric_definition_data) === null || _currentVariant$fabri2 === void 0 ? void 0 : _currentVariant$fabri2.fabric_name) || 'Unknown'\n              } : null;\n\n              // Prepare the variant options for React-Select\n              const variantOptions = allFabricVariants.map(variant => {\n                var _variant$fabric_defin3, _variant$fabric_defin4;\n                // Check if this variant is already selected in another detail\n                const isAlreadySelected = isDuplicateFabricVariant(variant.id, index);\n                return {\n                  value: variant.id,\n                  label: `${((_variant$fabric_defin3 = variant.fabric_definition_data) === null || _variant$fabric_defin3 === void 0 ? void 0 : _variant$fabric_defin3.fabric_name) || 'Unknown'} - ${variant.color_name || variant.color} (${variant.available_yard || variant.total_yard} yards available)${isAlreadySelected ? ' - Already Selected' : ''}`,\n                  color: variant.color,\n                  available_yard: variant.available_yard || variant.total_yard,\n                  total_yard: variant.total_yard,\n                  fabric_name: ((_variant$fabric_defin4 = variant.fabric_definition_data) === null || _variant$fabric_defin4 === void 0 ? void 0 : _variant$fabric_defin4.fabric_name) || 'Unknown',\n                  isDisabled: isAlreadySelected // Disable options that are already selected\n                };\n              });\n              return /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3 border\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  className: \"d-flex justify-content-between align-items-center bg-light\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"mb-0\",\n                    children: [\"Detail #\", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-danger\",\n                    size: \"sm\",\n                    onClick: () => removeDetailRow(index),\n                    disabled: details.length === 1,\n                    children: [/*#__PURE__*/_jsxDEV(BsTrash, {\n                      className: \"me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 25\n                    }, this), \" Remove\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(Row, {\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Fabric Variant (Color)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 541,\n                            columnNumber: 41\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 541,\n                          columnNumber: 29\n                        }, this), loadingVariants ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                            animation: \"border\",\n                            size: \"sm\",\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 544,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"Loading variants...\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 545,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 543,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Select, {\n                            options: variantOptions,\n                            components: {\n                              Option: ColourOption\n                            },\n                            value: currentValue,\n                            onChange: selectedOption => {\n                              handleDetailChange(index, 'fabric_variant', selectedOption.value);\n                            },\n                            placeholder: \"Select Fabric Variant\",\n                            styles: {\n                              control: provided => ({\n                                ...provided,\n                                borderColor: '#ddd',\n                                boxShadow: 'none',\n                                height: '38px',\n                                '&:hover': {\n                                  borderColor: '#aaa'\n                                }\n                              }),\n                              valueContainer: provided => ({\n                                ...provided,\n                                height: '38px',\n                                padding: '0 8px'\n                              }),\n                              option: (provided, state) => ({\n                                ...provided,\n                                backgroundColor: state.isDisabled ? '#f8f9fa' : state.isSelected ? '#007bff' : state.isFocused ? '#e9ecef' : 'white',\n                                color: state.isDisabled ? '#6c757d' : state.isSelected ? 'white' : 'black',\n                                cursor: state.isDisabled ? 'not-allowed' : 'default'\n                              })\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 549,\n                            columnNumber: 33\n                          }, this), !detail.fabric_variant && validated && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-danger small mt-1\",\n                            children: \"Please select a fabric variant.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 591,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 540,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 539,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex justify-content-between align-items-center mb-1\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            className: \"mb-0\",\n                            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Yard Usage\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 602,\n                              columnNumber: 60\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 602,\n                            columnNumber: 31\n                          }, this), currentVariant && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"text-danger small\" : \"text-success small\",\n                            children: [\"Available: \", currentVariant.available_yard || currentVariant.total_yard, \" yards\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 604,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 601,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"number\",\n                          step: \"0.01\",\n                          min: \"0\",\n                          value: detail.yard_usage,\n                          onChange: e => handleDetailChange(index, 'yard_usage', e.target.value),\n                          required: true,\n                          placeholder: \"Enter yards used\",\n                          isInvalid: currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard),\n                          className: currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"border-danger\" : \"\",\n                          style: {\n                            height: '38px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 609,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                          type: \"invalid\",\n                          children: currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? `Exceeds available yards (${currentVariant.available_yard || currentVariant.total_yard} yards available)` : \"Please enter valid yard usage.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 621,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Size Quantities\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 630,\n                      columnNumber: 52\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    children: [[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size, sizeIndex) => {\n                      const sizeKey = size.toLowerCase();\n                      return /*#__PURE__*/_jsxDEV(Col, {\n                        xs: 6,\n                        sm: 4,\n                        md: 2,\n                        className: \"mb-3\",\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            className: \"text-center d-block\",\n                            children: size\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 637,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"number\",\n                            min: \"0\",\n                            value: detail[sizeKey],\n                            onChange: e => {\n                              const val = Math.max(0, parseInt(e.target.value || 0));\n                              handleDetailChange(index, sizeKey, val);\n                            },\n                            className: \"text-center\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 638,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 636,\n                          columnNumber: 31\n                        }, this)\n                      }, sizeIndex, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 635,\n                        columnNumber: 29\n                      }, this);\n                    }), /*#__PURE__*/_jsxDEV(Col, {\n                      xs: 6,\n                      sm: 4,\n                      md: 2,\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          className: \"text-center d-block\",\n                          children: \"Total\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 654,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"form-control text-center bg-light\",\n                          children: parseInt(detail.xs || 0) + parseInt(detail.s || 0) + parseInt(detail.m || 0) + parseInt(detail.l || 0) + parseInt(detail.xl || 0)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 655,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 653,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 631,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-end mb-4\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"border-0\",\n                style: {\n                  backgroundColor: \"#e8f4fe\"\n                },\n                children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex flex-column\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"me-2\",\n                        children: \"Total Quantities:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 675,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"XS: \", totalQuantities.xs]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 676,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"S: \", totalQuantities.s]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 677,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"M: \", totalQuantities.m]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"L: \", totalQuantities.l]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 679,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"XL: \", totalQuantities.xl]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 680,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"success\",\n                        className: \"ms-2\",\n                        children: [\"Total: \", totalQuantities.total]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 674,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"me-2\",\n                        children: \"Total Yard Usage:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 684,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"info\",\n                        children: [totalQuantities.yard_usage.toFixed(2), \" yards\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 685,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 683,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 673,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center mt-4\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"primary\",\n                size: \"lg\",\n                disabled: isSubmitting,\n                className: \"px-5\",\n                children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 23\n                  }, this), \"Submitting...\"]\n                }, void 0, true) : 'Submit Cutting Record'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPdfModal,\n      onHide: handleCloseModal,\n      size: \"lg\",\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Generate Cutting Record PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Would you like to generate a PDF for this cutting record?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 11\n        }, this), submittedRecord && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"The PDF will include the following information:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Product Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.product_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Fabric:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.fabric_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Cutting Date:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 21\n              }, this), \" \", new Date(submittedRecord.cutting_date).toLocaleDateString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Quantities:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 21\n              }, this), \" XS: \", submittedRecord.totalQuantities.xs, \", S: \", submittedRecord.totalQuantities.s, \", M: \", submittedRecord.totalQuantities.m, \", L: \", submittedRecord.totalQuantities.l, \", XL: \", submittedRecord.totalQuantities.xl]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Items:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.totalQuantities.total]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Yard Usage:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.totalQuantities.yard_usage.toFixed(2), \" yards\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCloseModal,\n          children: \"No, Skip\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: generatePDF,\n          children: [/*#__PURE__*/_jsxDEV(BsFilePdf, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 13\n          }, this), \" Generate PDF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 716,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AddCuttingRecord, \"2V6SDyYqz6cCxc0l3E1C2/XOyOE=\");\n_c = AddCuttingRecord;\nexport default AddCuttingRecord;\nvar _c;\n$RefreshReg$(_c, \"AddCuttingRecord\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Select", "RoleBasedNavBar", "Card", "Form", "<PERSON><PERSON>", "Row", "Col", "Spinner", "<PERSON><PERSON>", "Badge", "Modal", "BsScissors", "BsPlus", "BsTrash", "BsCheck2Circle", "BsExclamationTriangle", "BsFilePdf", "jsPDF", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddCuttingRecord", "_s", "fabricDefinitions", "setFabricDefinitions", "allFabricVariants", "setAllFabricVariants", "cuttingDate", "setCuttingDate", "description", "setDescription", "productName", "setProductName", "fabricGroups", "setFabricGroups", "id", "Date", "now", "fabric_definition", "variants", "fabric_variant", "yard_usage", "xs", "s", "m", "l", "xl", "loadingVariants", "setLoadingVariants", "error", "setError", "success", "setSuccess", "isSubmitting", "setIsSubmitting", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "validated", "setValidated", "showPdfModal", "setShowPdfModal", "submittedRecord", "setSubmittedRecord", "handleResize", "addEventListener", "removeEventListener", "get", "then", "res", "data", "catch", "err", "console", "addDetailRow", "setDetails", "details", "removeDetailRow", "index", "newDetails", "filter", "_", "i", "isDuplicateFabricVariant", "variantId", "currentIndex", "some", "detail", "idx", "handleDetailChange", "field", "value", "handleSubmit", "e", "preventDefault", "form", "currentTarget", "checkValidity", "stopPropagation", "hasValidDetails", "selectedVariants", "map", "Boolean", "uniqueVariants", "Set", "length", "yardValidationError", "for<PERSON>ach", "variant", "find", "v", "parseFloat", "available_yard", "total_yard", "color_name", "color", "payload", "cutting_date", "product_name", "response", "post", "fabricNames", "recordData", "_variant$fabric_defin", "_variant$fabric_defin2", "fabric_definition_data", "fabric_name", "add", "fabric_names", "Array", "from", "join", "totalQuantities", "errorMessage", "generatePDF", "pdf", "orientation", "unit", "format", "titleFontSize", "headingFontSize", "normalFontSize", "smallFontSize", "setFontSize", "setFont", "text", "align", "line", "generalInfoData", "toString", "toLocaleDateString", "yPos", "row", "headers", "col<PERSON><PERSON><PERSON>", "colPositions", "currentPos", "width", "push", "setFillColor", "rect", "header", "_detail$xs", "_detail$s", "_detail$m", "_detail$l", "_detail$xl", "total", "parseInt", "toFixed", "toLocaleString", "save", "handleCloseModal", "ColourOption", "innerRef", "innerProps", "ref", "style", "display", "alignItems", "padding", "children", "height", "backgroundColor", "marginRight", "border", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "reduce", "acc", "marginLeft", "transition", "className", "size", "borderRadius", "Body", "noValidate", "onSubmit", "md", "Group", "Label", "Control", "type", "onChange", "target", "placeholder", "required", "<PERSON><PERSON><PERSON>", "as", "rows", "onClick", "disabled", "_currentVariant$fabri", "_currentVariant$fabri2", "currentV<PERSON>t", "currentValue", "variantOptions", "_variant$fabric_defin3", "_variant$fabric_defin4", "isAlreadySelected", "isDisabled", "Header", "animation", "options", "components", "Option", "selectedOption", "styles", "control", "provided", "borderColor", "boxShadow", "valueContainer", "option", "state", "isSelected", "isFocused", "cursor", "step", "min", "isInvalid", "sizeIndex", "sizeKey", "toLowerCase", "sm", "val", "Math", "max", "bg", "role", "show", "onHide", "centered", "closeButton", "Title", "Footer", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/System Development Project/pri new/frontend/src/pages/AddCutting.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport Select from 'react-select';\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport { Card, <PERSON>, <PERSON><PERSON>, Row, Col, Spin<PERSON>, <PERSON><PERSON>, Badge, Modal } from 'react-bootstrap';\r\nimport { BsScissors, BsPlus, BsTrash, BsCheck2Circle, BsExclamationTriangle, BsFilePdf } from 'react-icons/bs';\r\nimport jsPDF from 'jspdf';\r\n\r\nconst AddCuttingRecord = () => {\r\n  // Overall cutting record fields\r\n  const [fabricDefinitions, setFabricDefinitions] = useState([]);\r\n  const [allFabricVariants, setAllFabricVariants] = useState([]);\r\n  const [cuttingDate, setCuttingDate] = useState('');\r\n  const [description, setDescription] = useState('');\r\n  const [productName, setProductName] = useState('');\r\n\r\n  // Fabric definition groups - each group has a fabric definition and its variants\r\n  const [fabricGroups, setFabricGroups] = useState([\r\n    {\r\n      id: Date.now(),\r\n      fabric_definition: '',\r\n      variants: [{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]\r\n    }\r\n  ]);\r\n\r\n  // Loading, error, success states\r\n  const [loadingVariants, setLoadingVariants] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [validated, setValidated] = useState(false);\r\n  const [showPdfModal, setShowPdfModal] = useState(false);\r\n  const [submittedRecord, setSubmittedRecord] = useState(null);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Fetch all fabric variants on mount\r\n  useEffect(() => {\r\n    setLoadingVariants(true);\r\n    axios.get(\"http://localhost:8000/api/fabric-variants/\")\r\n      .then((res) => {\r\n        setAllFabricVariants(res.data);\r\n        setLoadingVariants(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error('Error fetching fabric variants:', err);\r\n        setError('Failed to load fabric variants. Please try again.');\r\n        setLoadingVariants(false);\r\n      });\r\n  }, []);\r\n\r\n  // Add a new empty detail row\r\n  const addDetailRow = () => {\r\n    setDetails([...details, { fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]);\r\n  };\r\n\r\n  // Delete a detail row\r\n  const removeDetailRow = (index) => {\r\n    const newDetails = details.filter((_, i) => i !== index);\r\n    setDetails(newDetails);\r\n  };\r\n\r\n  // Check if a fabric variant is already selected in another detail row\r\n  const isDuplicateFabricVariant = (variantId, currentIndex) => {\r\n    return details.some((detail, idx) =>\r\n      idx !== currentIndex && detail.fabric_variant === variantId && variantId !== ''\r\n    );\r\n  };\r\n\r\n  // Handle change for each detail row field\r\n  const handleDetailChange = (index, field, value) => {\r\n    const newDetails = [...details];\r\n\r\n    // If changing fabric variant, check for duplicates\r\n    if (field === 'fabric_variant') {\r\n      if (isDuplicateFabricVariant(value, index)) {\r\n        setError(`This fabric variant is already selected in another detail. Please select a different variant.`);\r\n        return; // Don't update the state if duplicate\r\n      } else {\r\n        setError(''); // Clear error if no duplicate\r\n      }\r\n    }\r\n\r\n    newDetails[index][field] = value;\r\n    setDetails(newDetails);\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Form validation\r\n    const form = e.currentTarget;\r\n    if (form.checkValidity() === false) {\r\n      e.stopPropagation();\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    // Check if any detail has a fabric variant selected\r\n    const hasValidDetails = details.some(detail => detail.fabric_variant);\r\n    if (!hasValidDetails) {\r\n      setError('Please select at least one fabric variant for your cutting details.');\r\n      return;\r\n    }\r\n\r\n    // Check for duplicate fabric variants\r\n    const selectedVariants = details.map(detail => detail.fabric_variant).filter(Boolean);\r\n    const uniqueVariants = [...new Set(selectedVariants)];\r\n\r\n    if (selectedVariants.length !== uniqueVariants.length) {\r\n      setError('You have selected the same fabric variant in multiple details. Please use unique fabric variants for each detail.');\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    // Validate yard availability for each detail\r\n    let yardValidationError = false;\r\n    details.forEach(detail => {\r\n      if (detail.fabric_variant) {\r\n        const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\r\n        if (variant && parseFloat(detail.yard_usage) > (variant.available_yard || variant.total_yard)) {\r\n          yardValidationError = true;\r\n          setError(`Yard usage for ${variant.color_name || variant.color} exceeds available yards (${variant.available_yard || variant.total_yard} yards available).`);\r\n        }\r\n      }\r\n    });\r\n\r\n    if (yardValidationError) {\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    setValidated(true);\r\n    setIsSubmitting(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    const payload = {\r\n      cutting_date: cuttingDate,\r\n      description: description,\r\n      product_name: productName,\r\n      details: details\r\n    };\r\n\r\n    try {\r\n      const response = await axios.post(\"http://localhost:8000/api/cutting/cutting-records/\", payload);\r\n      setSuccess('Cutting record created successfully!');\r\n\r\n      // Store the submitted record for PDF generation\r\n      const fabricNames = new Set();\r\n      const recordData = {\r\n        ...response.data,\r\n        details: response.data.details.map(detail => {\r\n          const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\r\n          if (variant?.fabric_definition_data?.fabric_name) {\r\n            fabricNames.add(variant.fabric_definition_data.fabric_name);\r\n          }\r\n          return {\r\n            ...detail,\r\n            color: variant?.color || 'Unknown',\r\n            color_name: variant?.color_name || variant?.color || 'Unknown',\r\n            fabric_name: variant?.fabric_definition_data?.fabric_name || 'Unknown'\r\n          };\r\n        }),\r\n        fabric_names: Array.from(fabricNames).join(', ') || 'Unknown',\r\n        totalQuantities: totalQuantities\r\n      };\r\n\r\n      setSubmittedRecord(recordData);\r\n\r\n      // Show the PDF generation modal\r\n      setShowPdfModal(true);\r\n    } catch (err) {\r\n      console.error('Error creating cutting record:', err);\r\n      if (err.response && err.response.data) {\r\n        // Display more specific error message if available\r\n        const errorMessage = typeof err.response.data === 'string'\r\n          ? err.response.data\r\n          : 'Failed to create cutting record. Please check your inputs.';\r\n        setError(errorMessage);\r\n      } else {\r\n        setError('Failed to create cutting record. Please try again.');\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Function to generate PDF directly without using html2canvas\r\n  const generatePDF = () => {\r\n    if (!submittedRecord) return;\r\n\r\n    try {\r\n      // Create a new PDF document\r\n      const pdf = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Set font sizes and styles\r\n      const titleFontSize = 18;\r\n      const headingFontSize = 14;\r\n      const normalFontSize = 10;\r\n      const smallFontSize = 8;\r\n\r\n      // Add title\r\n      pdf.setFontSize(titleFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Cutting Record', 105, 20, { align: 'center' });\r\n\r\n      // Add general information section\r\n      pdf.setFontSize(headingFontSize);\r\n      pdf.text('General Information', 20, 35);\r\n\r\n      pdf.setFontSize(normalFontSize);\r\n      pdf.setFont('helvetica', 'normal');\r\n\r\n      // Draw table for general info\r\n      pdf.line(20, 40, 190, 40); // Top horizontal line\r\n\r\n      const generalInfoData = [\r\n        ['Record ID', submittedRecord.id.toString()],\r\n        ['Product Name', submittedRecord.product_name],\r\n        ['Fabrics Used', submittedRecord.fabric_names],\r\n        ['Cutting Date', new Date(submittedRecord.cutting_date).toLocaleDateString()],\r\n        ['Description', submittedRecord.description || 'N/A']\r\n      ];\r\n\r\n      let yPos = 45;\r\n      generalInfoData.forEach((row) => {\r\n        pdf.setFont('helvetica', 'bold');\r\n        pdf.text(row[0], 25, yPos);\r\n        pdf.setFont('helvetica', 'normal');\r\n        pdf.text(row[1], 80, yPos);\r\n        yPos += 8;\r\n        pdf.line(20, yPos - 3, 190, yPos - 3); // Horizontal line after each row\r\n      });\r\n\r\n      // Add fabric details section\r\n      pdf.setFontSize(headingFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Fabric Details', 20, yPos + 10);\r\n\r\n      // Table headers for fabric details\r\n      const headers = ['Fabric', 'Color', 'Yard Usage', 'XS', 'S', 'M', 'L', 'XL', 'Total'];\r\n      const colWidths = [35, 35, 20, 12, 12, 12, 12, 12, 15];\r\n\r\n      // Calculate starting positions for each column\r\n      const colPositions = [];\r\n      let currentPos = 20;\r\n      colWidths.forEach(width => {\r\n        colPositions.push(currentPos);\r\n        currentPos += width;\r\n      });\r\n\r\n      // Draw table header\r\n      yPos += 15;\r\n      pdf.setFontSize(normalFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n\r\n      // Draw header background\r\n      pdf.setFillColor(240, 240, 240);\r\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\r\n\r\n      // Draw header text\r\n      headers.forEach((header, index) => {\r\n        pdf.text(header, colPositions[index] + 2, yPos);\r\n      });\r\n\r\n      // Draw horizontal line after header\r\n      yPos += 3;\r\n      pdf.line(20, yPos, 190, yPos);\r\n\r\n      // Draw table rows\r\n      pdf.setFont('helvetica', 'normal');\r\n      submittedRecord.details.forEach((detail) => {\r\n        yPos += 8;\r\n\r\n        // Calculate total for this row\r\n        const total = parseInt(detail.xs || 0) +\r\n                      parseInt(detail.s || 0) +\r\n                      parseInt(detail.m || 0) +\r\n                      parseInt(detail.l || 0) +\r\n                      parseInt(detail.xl || 0);\r\n\r\n        // Draw row data\r\n        pdf.text(detail.fabric_name || 'Unknown', colPositions[0] + 2, yPos);\r\n        pdf.text(detail.color_name || detail.color, colPositions[1] + 2, yPos);\r\n        pdf.text(`${detail.yard_usage} yards`, colPositions[2] + 2, yPos);\r\n        pdf.text(detail.xs?.toString() || '0', colPositions[3] + 2, yPos);\r\n        pdf.text(detail.s?.toString() || '0', colPositions[4] + 2, yPos);\r\n        pdf.text(detail.m?.toString() || '0', colPositions[5] + 2, yPos);\r\n        pdf.text(detail.l?.toString() || '0', colPositions[6] + 2, yPos);\r\n        pdf.text(detail.xl?.toString() || '0', colPositions[7] + 2, yPos);\r\n        pdf.text(total.toString(), colPositions[8] + 2, yPos);\r\n\r\n        // Draw horizontal line after row\r\n        yPos += 3;\r\n        pdf.line(20, yPos, 190, yPos);\r\n      });\r\n\r\n      // Draw totals row\r\n      yPos += 8;\r\n      pdf.setFillColor(240, 240, 240);\r\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\r\n\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Total', colPositions[0] + 2, yPos);\r\n      pdf.text('', colPositions[1] + 2, yPos);\r\n      pdf.text(`${submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards`, colPositions[2] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.xs.toString(), colPositions[3] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.s.toString(), colPositions[4] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.m.toString(), colPositions[5] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.l.toString(), colPositions[6] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.xl.toString(), colPositions[7] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.total.toString(), colPositions[8] + 2, yPos);\r\n\r\n      // Add footer\r\n      pdf.setFontSize(smallFontSize);\r\n      pdf.setFont('helvetica', 'italic');\r\n      pdf.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, { align: 'center' });\r\n      pdf.text('Fashion Garment Management System', 105, 285, { align: 'center' });\r\n\r\n      // Save the PDF\r\n      pdf.save(`Cutting_Record_${submittedRecord.id}_${submittedRecord.product_name}.pdf`);\r\n\r\n      // Reset form after PDF generation\r\n      setShowPdfModal(false);\r\n      setCuttingDate('');\r\n      setDescription('');\r\n      setProductName('');\r\n      setDetails([{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]);\r\n      setValidated(false);\r\n    } catch (error) {\r\n      console.error('Error generating PDF:', error);\r\n      setError('Failed to generate PDF. Please try again.');\r\n      setShowPdfModal(false);\r\n    }\r\n  };\r\n\r\n  // Function to handle modal close without generating PDF\r\n  const handleCloseModal = () => {\r\n    setShowPdfModal(false);\r\n    // Reset form\r\n    setCuttingDate('');\r\n    setDescription('');\r\n    setProductName('');\r\n    setDetails([{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]);\r\n    setValidated(false);\r\n  };\r\n\r\n  // Custom option component that shows a color swatch + label\r\n  const ColourOption = ({ data, innerRef, innerProps }) => (\r\n    <div\r\n      ref={innerRef}\r\n      {...innerProps}\r\n      style={{ display: 'flex', alignItems: 'center', padding: '4px' }}\r\n    >\r\n      <div\r\n        style={{\r\n          width: 16,\r\n          height: 16,\r\n          backgroundColor: data.color,\r\n          marginRight: 8,\r\n          border: '1px solid #ccc'\r\n        }}\r\n      />\r\n      <span>{data.label}</span>\r\n    </div>\r\n  );\r\n\r\n  // Calculate total quantities for all details\r\n  const totalQuantities = details.reduce(\r\n    (acc, detail) => {\r\n      acc.xs += parseInt(detail.xs) || 0;\r\n      acc.s += parseInt(detail.s) || 0;\r\n      acc.m += parseInt(detail.m) || 0;\r\n      acc.l += parseInt(detail.l) || 0;\r\n      acc.xl += parseInt(detail.xl) || 0;\r\n      acc.total += (parseInt(detail.xs) || 0) +\r\n                  (parseInt(detail.s) || 0) +\r\n                  (parseInt(detail.m) || 0) +\r\n                  (parseInt(detail.l) || 0) +\r\n                  (parseInt(detail.xl) || 0);\r\n      acc.yard_usage += parseFloat(detail.yard_usage) || 0;\r\n      return acc;\r\n    },\r\n    { xs: 0, s: 0, m: 0, l: 0, xl: 0, total: 0, yard_usage: 0 }\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <h2 className=\"mb-4\">\r\n          <BsScissors className=\"me-2\" />\r\n          Add Cutting Record\r\n        </h2>\r\n\r\n        {success && (\r\n          <Alert variant=\"success\" className=\"d-flex align-items-center\">\r\n            <BsCheck2Circle className=\"me-2\" size={20} />\r\n            {success}\r\n          </Alert>\r\n        )}\r\n\r\n        {error && (\r\n          <Alert variant=\"danger\" className=\"d-flex align-items-center\">\r\n            <BsExclamationTriangle className=\"me-2\" size={20} />\r\n            {error}\r\n          </Alert>\r\n        )}\r\n\r\n        <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n          <Card.Body>\r\n            <Form noValidate validated={validated} onSubmit={handleSubmit}>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Product Name</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={productName}\r\n                      onChange={(e) => setProductName(e.target.value)}\r\n                      placeholder=\"Enter product name\"\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please provide a product name.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Cutting Date</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={cuttingDate}\r\n                      onChange={(e) => setCuttingDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please select a cutting date.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Description</strong></Form.Label>\r\n                    <Form.Control\r\n                      as=\"textarea\"\r\n                      rows={3}\r\n                      value={description}\r\n                      onChange={(e) => setDescription(e.target.value)}\r\n                      placeholder=\"Enter details about this cutting record...\"\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <div className=\"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\">\r\n                <h4 className=\"mb-0\">Fabric Details</h4>\r\n                <Button\r\n                  variant=\"success\"\r\n                  size=\"sm\"\r\n                  onClick={addDetailRow}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  <BsPlus className=\"me-1\" /> Add Fabric Variant\r\n                </Button>\r\n              </div>\r\n\r\n              {details.map((detail, index) => {\r\n                // Find the selected variant object to set the value in React-Select\r\n                const currentVariant = allFabricVariants.find(v => v.id === detail.fabric_variant);\r\n                const currentValue = currentVariant\r\n                  ? {\r\n                      value: currentVariant.id,\r\n                      label: `${currentVariant.fabric_definition_data?.fabric_name || 'Unknown'} - ${currentVariant.color_name || currentVariant.color} (${currentVariant.available_yard || currentVariant.total_yard} yards available)`,\r\n                      color: currentVariant.color,\r\n                      available_yard: currentVariant.available_yard || currentVariant.total_yard,\r\n                      total_yard: currentVariant.total_yard,\r\n                      fabric_name: currentVariant.fabric_definition_data?.fabric_name || 'Unknown'\r\n                    }\r\n                  : null;\r\n\r\n                // Prepare the variant options for React-Select\r\n                const variantOptions = allFabricVariants.map((variant) => {\r\n                  // Check if this variant is already selected in another detail\r\n                  const isAlreadySelected = isDuplicateFabricVariant(variant.id, index);\r\n\r\n                  return {\r\n                    value: variant.id,\r\n                    label: `${variant.fabric_definition_data?.fabric_name || 'Unknown'} - ${variant.color_name || variant.color} (${variant.available_yard || variant.total_yard} yards available)${isAlreadySelected ? ' - Already Selected' : ''}`,\r\n                    color: variant.color,\r\n                    available_yard: variant.available_yard || variant.total_yard,\r\n                    total_yard: variant.total_yard,\r\n                    fabric_name: variant.fabric_definition_data?.fabric_name || 'Unknown',\r\n                    isDisabled: isAlreadySelected // Disable options that are already selected\r\n                  };\r\n                });\r\n\r\n                return (\r\n                  <Card key={index} className=\"mb-3 border\">\r\n                    <Card.Header className=\"d-flex justify-content-between align-items-center bg-light\">\r\n                      <h5 className=\"mb-0\">Detail #{index + 1}</h5>\r\n                      <Button\r\n                        variant=\"outline-danger\"\r\n                        size=\"sm\"\r\n                        onClick={() => removeDetailRow(index)}\r\n                        disabled={details.length === 1}\r\n                      >\r\n                        <BsTrash className=\"me-1\" /> Remove\r\n                      </Button>\r\n                    </Card.Header>\r\n                    <Card.Body>\r\n                      <Row>\r\n                        <Col md={6}>\r\n                          <Form.Group className=\"mb-3\">\r\n                            <Form.Label><strong>Fabric Variant (Color)</strong></Form.Label>\r\n                            {loadingVariants ? (\r\n                              <div className=\"d-flex align-items-center\">\r\n                                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                                <span>Loading variants...</span>\r\n                              </div>\r\n                            ) : (\r\n                              <>\r\n                                <Select\r\n                                  options={variantOptions}\r\n                                  components={{ Option: ColourOption }}\r\n                                  value={currentValue}\r\n                                  onChange={(selectedOption) => {\r\n                                    handleDetailChange(index, 'fabric_variant', selectedOption.value);\r\n                                  }}\r\n                                  placeholder=\"Select Fabric Variant\"\r\n                                  styles={{\r\n                                    control: (provided) => ({\r\n                                      ...provided,\r\n                                      borderColor: '#ddd',\r\n                                      boxShadow: 'none',\r\n                                      height: '38px',\r\n                                      '&:hover': {\r\n                                        borderColor: '#aaa'\r\n                                      }\r\n                                    }),\r\n                                    valueContainer: (provided) => ({\r\n                                      ...provided,\r\n                                      height: '38px',\r\n                                      padding: '0 8px'\r\n                                    }),\r\n                                    option: (provided, state) => ({\r\n                                      ...provided,\r\n                                      backgroundColor: state.isDisabled\r\n                                        ? '#f8f9fa'\r\n                                        : state.isSelected\r\n                                          ? '#007bff'\r\n                                          : state.isFocused\r\n                                            ? '#e9ecef'\r\n                                            : 'white',\r\n                                      color: state.isDisabled\r\n                                        ? '#6c757d'\r\n                                        : state.isSelected\r\n                                          ? 'white'\r\n                                          : 'black',\r\n                                      cursor: state.isDisabled ? 'not-allowed' : 'default'\r\n                                    })\r\n                                  }}\r\n                                />\r\n                                {!detail.fabric_variant && validated && (\r\n                                  <div className=\"text-danger small mt-1\">\r\n                                    Please select a fabric variant.\r\n                                  </div>\r\n                                )}\r\n                              </>\r\n                            )}\r\n                          </Form.Group>\r\n                        </Col>\r\n                        <Col md={6}>\r\n                          <Form.Group className=\"mb-3\">\r\n                            <div className=\"d-flex justify-content-between align-items-center mb-1\">\r\n                              <Form.Label className=\"mb-0\"><strong>Yard Usage</strong></Form.Label>\r\n                              {currentVariant && (\r\n                                <span className={parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"text-danger small\" : \"text-success small\"}>\r\n                                  Available: {currentVariant.available_yard || currentVariant.total_yard} yards\r\n                                </span>\r\n                              )}\r\n                            </div>\r\n                            <Form.Control\r\n                              type=\"number\"\r\n                              step=\"0.01\"\r\n                              min=\"0\"\r\n                              value={detail.yard_usage}\r\n                              onChange={(e) => handleDetailChange(index, 'yard_usage', e.target.value)}\r\n                              required\r\n                              placeholder=\"Enter yards used\"\r\n                              isInvalid={currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard)}\r\n                              className={currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"border-danger\" : \"\"}\r\n                              style={{ height: '38px' }}\r\n                            />\r\n                            <Form.Control.Feedback type=\"invalid\">\r\n                              {currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard)\r\n                                ? `Exceeds available yards (${currentVariant.available_yard || currentVariant.total_yard} yards available)`\r\n                                : \"Please enter valid yard usage.\"}\r\n                            </Form.Control.Feedback>\r\n                          </Form.Group>\r\n                        </Col>\r\n                      </Row>\r\n\r\n                      <Form.Label className=\"mt-2\"><strong>Size Quantities</strong></Form.Label>\r\n                      <Row>\r\n                        {[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size, sizeIndex) => {\r\n                          const sizeKey = size.toLowerCase();\r\n                          return (\r\n                            <Col key={sizeIndex} xs={6} sm={4} md={2} className=\"mb-3\">\r\n                              <Form.Group>\r\n                                <Form.Label className=\"text-center d-block\">{size}</Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail[sizeKey]}\r\n                                  onChange={(e) => {\r\n                                    const val = Math.max(0, parseInt(e.target.value || 0));\r\n                                    handleDetailChange(index, sizeKey, val);\r\n                                  }}\r\n                                  className=\"text-center\"\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                          );\r\n                        })}\r\n                        <Col xs={6} sm={4} md={2} className=\"mb-3\">\r\n                          <Form.Group>\r\n                            <Form.Label className=\"text-center d-block\">Total</Form.Label>\r\n                            <div className=\"form-control text-center bg-light\">\r\n                              {parseInt(detail.xs || 0) +\r\n                               parseInt(detail.s || 0) +\r\n                               parseInt(detail.m || 0) +\r\n                               parseInt(detail.l || 0) +\r\n                               parseInt(detail.xl || 0)}\r\n                            </div>\r\n                          </Form.Group>\r\n                        </Col>\r\n                      </Row>\r\n                    </Card.Body>\r\n                  </Card>\r\n                );\r\n              })}\r\n\r\n              <div className=\"d-flex justify-content-end mb-4\">\r\n                <Card className=\"border-0\" style={{ backgroundColor: \"#e8f4fe\" }}>\r\n                  <Card.Body className=\"py-2\">\r\n                    <div className=\"d-flex flex-column\">\r\n                      <div className=\"d-flex align-items-center mb-2\">\r\n                        <strong className=\"me-2\">Total Quantities:</strong>\r\n                        <Badge bg=\"primary\" className=\"me-1\">XS: {totalQuantities.xs}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">S: {totalQuantities.s}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">M: {totalQuantities.m}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">L: {totalQuantities.l}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">XL: {totalQuantities.xl}</Badge>\r\n                        <Badge bg=\"success\" className=\"ms-2\">Total: {totalQuantities.total}</Badge>\r\n                      </div>\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <strong className=\"me-2\">Total Yard Usage:</strong>\r\n                        <Badge bg=\"info\">{totalQuantities.yard_usage.toFixed(2)} yards</Badge>\r\n                      </div>\r\n                    </div>\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n\r\n              <div className=\"d-flex justify-content-center mt-4\">\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"primary\"\r\n                  size=\"lg\"\r\n                  disabled={isSubmitting}\r\n                  className=\"px-5\"\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                      Submitting...\r\n                    </>\r\n                  ) : (\r\n                    'Submit Cutting Record'\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* PDF Generation Modal */}\r\n      <Modal show={showPdfModal} onHide={handleCloseModal} size=\"lg\" centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Generate Cutting Record PDF</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p>Would you like to generate a PDF for this cutting record?</p>\r\n\r\n          {submittedRecord && (\r\n            <div className=\"mb-3\">\r\n              <p>The PDF will include the following information:</p>\r\n              <ul>\r\n                <li><strong>Product Name:</strong> {submittedRecord.product_name}</li>\r\n                <li><strong>Fabric:</strong> {submittedRecord.fabric_name}</li>\r\n                <li><strong>Cutting Date:</strong> {new Date(submittedRecord.cutting_date).toLocaleDateString()}</li>\r\n                <li><strong>Total Quantities:</strong> XS: {submittedRecord.totalQuantities.xs},\r\n                  S: {submittedRecord.totalQuantities.s},\r\n                  M: {submittedRecord.totalQuantities.m},\r\n                  L: {submittedRecord.totalQuantities.l},\r\n                  XL: {submittedRecord.totalQuantities.xl}</li>\r\n                <li><strong>Total Items:</strong> {submittedRecord.totalQuantities.total}</li>\r\n                <li><strong>Total Yard Usage:</strong> {submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards</li>\r\n              </ul>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={handleCloseModal}>\r\n            No, Skip\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={generatePDF}>\r\n            <BsFilePdf className=\"me-2\" /> Generate PDF\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AddCuttingRecord;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AAC5F,SAASC,UAAU,EAAEC,MAAM,EAAEC,OAAO,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,SAAS,QAAQ,gBAAgB;AAC9G,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC6B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,CAC/C;IACEuC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;IACdC,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,CAAC;MAAEC,cAAc,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAC;EACnF,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2D,aAAa,EAAEC,gBAAgB,CAAC,GAAG5D,QAAQ,CAAC6D,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAC5E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiE,YAAY,EAAEC,eAAe,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoE,YAAY,GAAGA,CAAA,KAAM;MACzBT,gBAAgB,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5C,CAAC;IAEDD,MAAM,CAACS,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMR,MAAM,CAACU,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApE,SAAS,CAAC,MAAM;IACdmD,kBAAkB,CAAC,IAAI,CAAC;IACxBlD,KAAK,CAACsE,GAAG,CAAC,4CAA4C,CAAC,CACpDC,IAAI,CAAEC,GAAG,IAAK;MACb5C,oBAAoB,CAAC4C,GAAG,CAACC,IAAI,CAAC;MAC9BvB,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC,CACDwB,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACzB,KAAK,CAAC,iCAAiC,EAAEwB,GAAG,CAAC;MACrDvB,QAAQ,CAAC,mDAAmD,CAAC;MAC7DF,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2B,YAAY,GAAGA,CAAA,KAAM;IACzBC,UAAU,CAAC,CAAC,GAAGC,OAAO,EAAE;MAAErC,cAAc,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAC,CAAC,CAAC;EAClG,CAAC;;EAED;EACA,MAAMgC,eAAe,GAAIC,KAAK,IAAK;IACjC,MAAMC,UAAU,GAAGH,OAAO,CAACI,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IACxDH,UAAU,CAACI,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMI,wBAAwB,GAAGA,CAACC,SAAS,EAAEC,YAAY,KAAK;IAC5D,OAAOT,OAAO,CAACU,IAAI,CAAC,CAACC,MAAM,EAAEC,GAAG,KAC9BA,GAAG,KAAKH,YAAY,IAAIE,MAAM,CAAChD,cAAc,KAAK6C,SAAS,IAAIA,SAAS,KAAK,EAC/E,CAAC;EACH,CAAC;;EAED;EACA,MAAMK,kBAAkB,GAAGA,CAACX,KAAK,EAAEY,KAAK,EAAEC,KAAK,KAAK;IAClD,MAAMZ,UAAU,GAAG,CAAC,GAAGH,OAAO,CAAC;;IAE/B;IACA,IAAIc,KAAK,KAAK,gBAAgB,EAAE;MAC9B,IAAIP,wBAAwB,CAACQ,KAAK,EAAEb,KAAK,CAAC,EAAE;QAC1C7B,QAAQ,CAAC,+FAA+F,CAAC;QACzG,OAAO,CAAC;MACV,CAAC,MAAM;QACLA,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB;IACF;IAEA8B,UAAU,CAACD,KAAK,CAAC,CAACY,KAAK,CAAC,GAAGC,KAAK;IAChChB,UAAU,CAACI,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMa,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,IAAI,GAAGF,CAAC,CAACG,aAAa;IAC5B,IAAID,IAAI,CAACE,aAAa,CAAC,CAAC,KAAK,KAAK,EAAE;MAClCJ,CAAC,CAACK,eAAe,CAAC,CAAC;MACnBvC,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;;IAEA;IACA,MAAMwC,eAAe,GAAGvB,OAAO,CAACU,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAChD,cAAc,CAAC;IACrE,IAAI,CAAC4D,eAAe,EAAE;MACpBlD,QAAQ,CAAC,qEAAqE,CAAC;MAC/E;IACF;;IAEA;IACA,MAAMmD,gBAAgB,GAAGxB,OAAO,CAACyB,GAAG,CAACd,MAAM,IAAIA,MAAM,CAAChD,cAAc,CAAC,CAACyC,MAAM,CAACsB,OAAO,CAAC;IACrF,MAAMC,cAAc,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACJ,gBAAgB,CAAC,CAAC;IAErD,IAAIA,gBAAgB,CAACK,MAAM,KAAKF,cAAc,CAACE,MAAM,EAAE;MACrDxD,QAAQ,CAAC,mHAAmH,CAAC;MAC7HU,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;;IAEA;IACA,IAAI+C,mBAAmB,GAAG,KAAK;IAC/B9B,OAAO,CAAC+B,OAAO,CAACpB,MAAM,IAAI;MACxB,IAAIA,MAAM,CAAChD,cAAc,EAAE;QACzB,MAAMqE,OAAO,GAAGpF,iBAAiB,CAACqF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5E,EAAE,KAAKqD,MAAM,CAAChD,cAAc,CAAC;QAC3E,IAAIqE,OAAO,IAAIG,UAAU,CAACxB,MAAM,CAAC/C,UAAU,CAAC,IAAIoE,OAAO,CAACI,cAAc,IAAIJ,OAAO,CAACK,UAAU,CAAC,EAAE;UAC7FP,mBAAmB,GAAG,IAAI;UAC1BzD,QAAQ,CAAC,kBAAkB2D,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACO,KAAK,6BAA6BP,OAAO,CAACI,cAAc,IAAIJ,OAAO,CAACK,UAAU,oBAAoB,CAAC;QAC9J;MACF;IACF,CAAC,CAAC;IAEF,IAAIP,mBAAmB,EAAE;MACvB/C,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;IAEAA,YAAY,CAAC,IAAI,CAAC;IAClBN,eAAe,CAAC,IAAI,CAAC;IACrBJ,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,MAAMiE,OAAO,GAAG;MACdC,YAAY,EAAE3F,WAAW;MACzBE,WAAW,EAAEA,WAAW;MACxB0F,YAAY,EAAExF,WAAW;MACzB8C,OAAO,EAAEA;IACX,CAAC;IAED,IAAI;MACF,MAAM2C,QAAQ,GAAG,MAAM1H,KAAK,CAAC2H,IAAI,CAAC,oDAAoD,EAAEJ,OAAO,CAAC;MAChGjE,UAAU,CAAC,sCAAsC,CAAC;;MAElD;MACA,MAAMsE,WAAW,GAAG,IAAIjB,GAAG,CAAC,CAAC;MAC7B,MAAMkB,UAAU,GAAG;QACjB,GAAGH,QAAQ,CAACjD,IAAI;QAChBM,OAAO,EAAE2C,QAAQ,CAACjD,IAAI,CAACM,OAAO,CAACyB,GAAG,CAACd,MAAM,IAAI;UAAA,IAAAoC,qBAAA,EAAAC,sBAAA;UAC3C,MAAMhB,OAAO,GAAGpF,iBAAiB,CAACqF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5E,EAAE,KAAKqD,MAAM,CAAChD,cAAc,CAAC;UAC3E,IAAIqE,OAAO,aAAPA,OAAO,gBAAAe,qBAAA,GAAPf,OAAO,CAAEiB,sBAAsB,cAAAF,qBAAA,eAA/BA,qBAAA,CAAiCG,WAAW,EAAE;YAChDL,WAAW,CAACM,GAAG,CAACnB,OAAO,CAACiB,sBAAsB,CAACC,WAAW,CAAC;UAC7D;UACA,OAAO;YACL,GAAGvC,MAAM;YACT4B,KAAK,EAAE,CAAAP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEO,KAAK,KAAI,SAAS;YAClCD,UAAU,EAAE,CAAAN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEM,UAAU,MAAIN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEO,KAAK,KAAI,SAAS;YAC9DW,WAAW,EAAE,CAAAlB,OAAO,aAAPA,OAAO,wBAAAgB,sBAAA,GAAPhB,OAAO,CAAEiB,sBAAsB,cAAAD,sBAAA,uBAA/BA,sBAAA,CAAiCE,WAAW,KAAI;UAC/D,CAAC;QACH,CAAC,CAAC;QACFE,YAAY,EAAEC,KAAK,CAACC,IAAI,CAACT,WAAW,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC,IAAI,SAAS;QAC7DC,eAAe,EAAEA;MACnB,CAAC;MAEDrE,kBAAkB,CAAC2D,UAAU,CAAC;;MAE9B;MACA7D,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,gCAAgC,EAAEwB,GAAG,CAAC;MACpD,IAAIA,GAAG,CAAC+C,QAAQ,IAAI/C,GAAG,CAAC+C,QAAQ,CAACjD,IAAI,EAAE;QACrC;QACA,MAAM+D,YAAY,GAAG,OAAO7D,GAAG,CAAC+C,QAAQ,CAACjD,IAAI,KAAK,QAAQ,GACtDE,GAAG,CAAC+C,QAAQ,CAACjD,IAAI,GACjB,4DAA4D;QAChErB,QAAQ,CAACoF,YAAY,CAAC;MACxB,CAAC,MAAM;QACLpF,QAAQ,CAAC,oDAAoD,CAAC;MAChE;IACF,CAAC,SAAS;MACRI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMiF,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACxE,eAAe,EAAE;IAEtB,IAAI;MACF;MACA,MAAMyE,GAAG,GAAG,IAAIxH,KAAK,CAAC;QACpByH,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE;MACV,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG,EAAE;MACxB,MAAMC,eAAe,GAAG,EAAE;MAC1B,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,aAAa,GAAG,CAAC;;MAEvB;MACAP,GAAG,CAACQ,WAAW,CAACJ,aAAa,CAAC;MAC9BJ,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAExD;MACAX,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACU,IAAI,CAAC,qBAAqB,EAAE,EAAE,EAAE,EAAE,CAAC;MAEvCV,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;;MAElC;MACAT,GAAG,CAACY,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;;MAE3B,MAAMC,eAAe,GAAG,CACtB,CAAC,WAAW,EAAEtF,eAAe,CAAC5B,EAAE,CAACmH,QAAQ,CAAC,CAAC,CAAC,EAC5C,CAAC,cAAc,EAAEvF,eAAe,CAACwD,YAAY,CAAC,EAC9C,CAAC,cAAc,EAAExD,eAAe,CAACkE,YAAY,CAAC,EAC9C,CAAC,cAAc,EAAE,IAAI7F,IAAI,CAAC2B,eAAe,CAACuD,YAAY,CAAC,CAACiC,kBAAkB,CAAC,CAAC,CAAC,EAC7E,CAAC,aAAa,EAAExF,eAAe,CAAClC,WAAW,IAAI,KAAK,CAAC,CACtD;MAED,IAAI2H,IAAI,GAAG,EAAE;MACbH,eAAe,CAACzC,OAAO,CAAE6C,GAAG,IAAK;QAC/BjB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCT,GAAG,CAACU,IAAI,CAACO,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAED,IAAI,CAAC;QAC1BhB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClCT,GAAG,CAACU,IAAI,CAACO,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAED,IAAI,CAAC;QAC1BA,IAAI,IAAI,CAAC;QACThB,GAAG,CAACY,IAAI,CAAC,EAAE,EAAEI,IAAI,GAAG,CAAC,EAAE,GAAG,EAAEA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC;;MAEF;MACAhB,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAEM,IAAI,GAAG,EAAE,CAAC;;MAEzC;MACA,MAAME,OAAO,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC;MACrF,MAAMC,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;MAEtD;MACA,MAAMC,YAAY,GAAG,EAAE;MACvB,IAAIC,UAAU,GAAG,EAAE;MACnBF,SAAS,CAAC/C,OAAO,CAACkD,KAAK,IAAI;QACzBF,YAAY,CAACG,IAAI,CAACF,UAAU,CAAC;QAC7BA,UAAU,IAAIC,KAAK;MACrB,CAAC,CAAC;;MAEF;MACAN,IAAI,IAAI,EAAE;MACVhB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;;MAEhC;MACAT,GAAG,CAACwB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BxB,GAAG,CAACyB,IAAI,CAAC,EAAE,EAAET,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;;MAEnC;MACAE,OAAO,CAAC9C,OAAO,CAAC,CAACsD,MAAM,EAAEnF,KAAK,KAAK;QACjCyD,GAAG,CAACU,IAAI,CAACgB,MAAM,EAAEN,YAAY,CAAC7E,KAAK,CAAC,GAAG,CAAC,EAAEyE,IAAI,CAAC;MACjD,CAAC,CAAC;;MAEF;MACAA,IAAI,IAAI,CAAC;MACThB,GAAG,CAACY,IAAI,CAAC,EAAE,EAAEI,IAAI,EAAE,GAAG,EAAEA,IAAI,CAAC;;MAE7B;MACAhB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClClF,eAAe,CAACc,OAAO,CAAC+B,OAAO,CAAEpB,MAAM,IAAK;QAAA,IAAA2E,UAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,UAAA;QAC1Cf,IAAI,IAAI,CAAC;;QAET;QACA,MAAMgB,KAAK,GAAGC,QAAQ,CAACjF,MAAM,CAAC9C,EAAE,IAAI,CAAC,CAAC,GACxB+H,QAAQ,CAACjF,MAAM,CAAC7C,CAAC,IAAI,CAAC,CAAC,GACvB8H,QAAQ,CAACjF,MAAM,CAAC5C,CAAC,IAAI,CAAC,CAAC,GACvB6H,QAAQ,CAACjF,MAAM,CAAC3C,CAAC,IAAI,CAAC,CAAC,GACvB4H,QAAQ,CAACjF,MAAM,CAAC1C,EAAE,IAAI,CAAC,CAAC;;QAEtC;QACA0F,GAAG,CAACU,IAAI,CAAC1D,MAAM,CAACuC,WAAW,IAAI,SAAS,EAAE6B,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACpEhB,GAAG,CAACU,IAAI,CAAC1D,MAAM,CAAC2B,UAAU,IAAI3B,MAAM,CAAC4B,KAAK,EAAEwC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACtEhB,GAAG,CAACU,IAAI,CAAC,GAAG1D,MAAM,CAAC/C,UAAU,QAAQ,EAAEmH,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACjEhB,GAAG,CAACU,IAAI,CAAC,EAAAiB,UAAA,GAAA3E,MAAM,CAAC9C,EAAE,cAAAyH,UAAA,uBAATA,UAAA,CAAWb,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACjEhB,GAAG,CAACU,IAAI,CAAC,EAAAkB,SAAA,GAAA5E,MAAM,CAAC7C,CAAC,cAAAyH,SAAA,uBAARA,SAAA,CAAUd,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAmB,SAAA,GAAA7E,MAAM,CAAC5C,CAAC,cAAAyH,SAAA,uBAARA,SAAA,CAAUf,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAoB,SAAA,GAAA9E,MAAM,CAAC3C,CAAC,cAAAyH,SAAA,uBAARA,SAAA,CAAUhB,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QAChEhB,GAAG,CAACU,IAAI,CAAC,EAAAqB,UAAA,GAAA/E,MAAM,CAAC1C,EAAE,cAAAyH,UAAA,uBAATA,UAAA,CAAWjB,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACjEhB,GAAG,CAACU,IAAI,CAACsB,KAAK,CAAClB,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;;QAErD;QACAA,IAAI,IAAI,CAAC;QACThB,GAAG,CAACY,IAAI,CAAC,EAAE,EAAEI,IAAI,EAAE,GAAG,EAAEA,IAAI,CAAC;MAC/B,CAAC,CAAC;;MAEF;MACAA,IAAI,IAAI,CAAC;MACThB,GAAG,CAACwB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BxB,GAAG,CAACyB,IAAI,CAAC,EAAE,EAAET,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;MAEnChB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,OAAO,EAAEU,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MAC5ChB,GAAG,CAACU,IAAI,CAAC,EAAE,EAAEU,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACvChB,GAAG,CAACU,IAAI,CAAC,GAAGnF,eAAe,CAACsE,eAAe,CAAC5F,UAAU,CAACiI,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAEd,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACrGhB,GAAG,CAACU,IAAI,CAACnF,eAAe,CAACsE,eAAe,CAAC3F,EAAE,CAAC4G,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MAClFhB,GAAG,CAACU,IAAI,CAACnF,eAAe,CAACsE,eAAe,CAAC1F,CAAC,CAAC2G,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACjFhB,GAAG,CAACU,IAAI,CAACnF,eAAe,CAACsE,eAAe,CAACzF,CAAC,CAAC0G,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACjFhB,GAAG,CAACU,IAAI,CAACnF,eAAe,CAACsE,eAAe,CAACxF,CAAC,CAACyG,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACjFhB,GAAG,CAACU,IAAI,CAACnF,eAAe,CAACsE,eAAe,CAACvF,EAAE,CAACwG,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MAClFhB,GAAG,CAACU,IAAI,CAACnF,eAAe,CAACsE,eAAe,CAACmC,KAAK,CAAClB,QAAQ,CAAC,CAAC,EAAEM,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;;MAErF;MACAhB,GAAG,CAACQ,WAAW,CAACD,aAAa,CAAC;MAC9BP,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCT,GAAG,CAACU,IAAI,CAAC,iBAAiB,IAAI9G,IAAI,CAAC,CAAC,CAACuI,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;QAAExB,KAAK,EAAE;MAAS,CAAC,CAAC;MACvFX,GAAG,CAACU,IAAI,CAAC,mCAAmC,EAAE,GAAG,EAAE,GAAG,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAE5E;MACAX,GAAG,CAACoC,IAAI,CAAC,kBAAkB7G,eAAe,CAAC5B,EAAE,IAAI4B,eAAe,CAACwD,YAAY,MAAM,CAAC;;MAEpF;MACAzD,eAAe,CAAC,KAAK,CAAC;MACtBlC,cAAc,CAAC,EAAE,CAAC;MAClBE,cAAc,CAAC,EAAE,CAAC;MAClBE,cAAc,CAAC,EAAE,CAAC;MAClB4C,UAAU,CAAC,CAAC;QAAEpC,cAAc,EAAE,EAAE;QAAEC,UAAU,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC,CAAC,CAAC;MACpFc,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,2CAA2C,CAAC;MACrDY,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM+G,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/G,eAAe,CAAC,KAAK,CAAC;IACtB;IACAlC,cAAc,CAAC,EAAE,CAAC;IAClBE,cAAc,CAAC,EAAE,CAAC;IAClBE,cAAc,CAAC,EAAE,CAAC;IAClB4C,UAAU,CAAC,CAAC;MAAEpC,cAAc,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAC,CAAC,CAAC;IACpFc,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMkH,YAAY,GAAGA,CAAC;IAAEvG,IAAI;IAAEwG,QAAQ;IAAEC;EAAW,CAAC,kBAClD9J,OAAA;IACE+J,GAAG,EAAEF,QAAS;IAAA,GACVC,UAAU;IACdE,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAM,CAAE;IAAAC,QAAA,gBAEjEpK,OAAA;MACEgK,KAAK,EAAE;QACLpB,KAAK,EAAE,EAAE;QACTyB,MAAM,EAAE,EAAE;QACVC,eAAe,EAAEjH,IAAI,CAAC6C,KAAK;QAC3BqE,WAAW,EAAE,CAAC;QACdC,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACF5K,OAAA;MAAAoK,QAAA,EAAO/G,IAAI,CAACwH;IAAK;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CACN;;EAED;EACA,MAAMzD,eAAe,GAAGxD,OAAO,CAACmH,MAAM,CACpC,CAACC,GAAG,EAAEzG,MAAM,KAAK;IACfyG,GAAG,CAACvJ,EAAE,IAAI+H,QAAQ,CAACjF,MAAM,CAAC9C,EAAE,CAAC,IAAI,CAAC;IAClCuJ,GAAG,CAACtJ,CAAC,IAAI8H,QAAQ,CAACjF,MAAM,CAAC7C,CAAC,CAAC,IAAI,CAAC;IAChCsJ,GAAG,CAACrJ,CAAC,IAAI6H,QAAQ,CAACjF,MAAM,CAAC5C,CAAC,CAAC,IAAI,CAAC;IAChCqJ,GAAG,CAACpJ,CAAC,IAAI4H,QAAQ,CAACjF,MAAM,CAAC3C,CAAC,CAAC,IAAI,CAAC;IAChCoJ,GAAG,CAACnJ,EAAE,IAAI2H,QAAQ,CAACjF,MAAM,CAAC1C,EAAE,CAAC,IAAI,CAAC;IAClCmJ,GAAG,CAACzB,KAAK,IAAI,CAACC,QAAQ,CAACjF,MAAM,CAAC9C,EAAE,CAAC,IAAI,CAAC,KACzB+H,QAAQ,CAACjF,MAAM,CAAC7C,CAAC,CAAC,IAAI,CAAC,CAAC,IACxB8H,QAAQ,CAACjF,MAAM,CAAC5C,CAAC,CAAC,IAAI,CAAC,CAAC,IACxB6H,QAAQ,CAACjF,MAAM,CAAC3C,CAAC,CAAC,IAAI,CAAC,CAAC,IACxB4H,QAAQ,CAACjF,MAAM,CAAC1C,EAAE,CAAC,IAAI,CAAC,CAAC;IACtCmJ,GAAG,CAACxJ,UAAU,IAAIuE,UAAU,CAACxB,MAAM,CAAC/C,UAAU,CAAC,IAAI,CAAC;IACpD,OAAOwJ,GAAG;EACZ,CAAC,EACD;IAAEvJ,EAAE,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,EAAE,EAAE,CAAC;IAAE0H,KAAK,EAAE,CAAC;IAAE/H,UAAU,EAAE;EAAE,CAC5D,CAAC;EAED,oBACEvB,OAAA,CAAAE,SAAA;IAAAkK,QAAA,gBACEpK,OAAA,CAAClB,eAAe;MAAA2L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnB5K,OAAA;MACEgK,KAAK,EAAE;QACLgB,UAAU,EAAE3I,aAAa,GAAG,OAAO,GAAG,MAAM;QAC5CuG,KAAK,EAAE,eAAevG,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG;QACzD4I,UAAU,EAAE,eAAe;QAC3Bd,OAAO,EAAE;MACX,CAAE;MAAAC,QAAA,gBAEFpK,OAAA;QAAIkL,SAAS,EAAC,MAAM;QAAAd,QAAA,gBAClBpK,OAAA,CAACR,UAAU;UAAC0L,SAAS,EAAC;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,sBAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEJ3I,OAAO,iBACNjC,OAAA,CAACX,KAAK;QAACsG,OAAO,EAAC,SAAS;QAACuF,SAAS,EAAC,2BAA2B;QAAAd,QAAA,gBAC5DpK,OAAA,CAACL,cAAc;UAACuL,SAAS,EAAC,MAAM;UAACC,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC5C3I,OAAO;MAAA;QAAAwI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,EAEA7I,KAAK,iBACJ/B,OAAA,CAACX,KAAK;QAACsG,OAAO,EAAC,QAAQ;QAACuF,SAAS,EAAC,2BAA2B;QAAAd,QAAA,gBAC3DpK,OAAA,CAACJ,qBAAqB;UAACsL,SAAS,EAAC,MAAM;UAACC,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACnD7I,KAAK;MAAA;QAAA0I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAED5K,OAAA,CAACjB,IAAI;QAACmM,SAAS,EAAC,gBAAgB;QAAClB,KAAK,EAAE;UAAEM,eAAe,EAAE,SAAS;UAAEc,YAAY,EAAE;QAAO,CAAE;QAAAhB,QAAA,eAC3FpK,OAAA,CAACjB,IAAI,CAACsM,IAAI;UAAAjB,QAAA,eACRpK,OAAA,CAAChB,IAAI;YAACsM,UAAU;YAAC7I,SAAS,EAAEA,SAAU;YAAC8I,QAAQ,EAAE5G,YAAa;YAAAyF,QAAA,gBAC5DpK,OAAA,CAACd,GAAG;cAAAkL,QAAA,eACFpK,OAAA,CAACb,GAAG;gBAACqM,EAAE,EAAE,CAAE;gBAAApB,QAAA,eACTpK,OAAA,CAAChB,IAAI,CAACyM,KAAK;kBAACP,SAAS,EAAC,MAAM;kBAAAd,QAAA,gBAC1BpK,OAAA,CAAChB,IAAI,CAAC0M,KAAK;oBAAAtB,QAAA,eAACpK,OAAA;sBAAAoK,QAAA,EAAQ;oBAAY;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtD5K,OAAA,CAAChB,IAAI,CAAC2M,OAAO;oBACXC,IAAI,EAAC,MAAM;oBACXlH,KAAK,EAAE7D,WAAY;oBACnBgL,QAAQ,EAAGjH,CAAC,IAAK9D,cAAc,CAAC8D,CAAC,CAACkH,MAAM,CAACpH,KAAK,CAAE;oBAChDqH,WAAW,EAAC,oBAAoB;oBAChCC,QAAQ;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACF5K,OAAA,CAAChB,IAAI,CAAC2M,OAAO,CAACM,QAAQ;oBAACL,IAAI,EAAC,SAAS;oBAAAxB,QAAA,EAAC;kBAEtC;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5K,OAAA,CAACd,GAAG;cAAAkL,QAAA,gBACFpK,OAAA,CAACb,GAAG;gBAACqM,EAAE,EAAE,CAAE;gBAAApB,QAAA,eACTpK,OAAA,CAAChB,IAAI,CAACyM,KAAK;kBAACP,SAAS,EAAC,MAAM;kBAAAd,QAAA,gBAC1BpK,OAAA,CAAChB,IAAI,CAAC0M,KAAK;oBAAAtB,QAAA,eAACpK,OAAA;sBAAAoK,QAAA,EAAQ;oBAAY;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtD5K,OAAA,CAAChB,IAAI,CAAC2M,OAAO;oBACXC,IAAI,EAAC,MAAM;oBACXlH,KAAK,EAAEjE,WAAY;oBACnBoL,QAAQ,EAAGjH,CAAC,IAAKlE,cAAc,CAACkE,CAAC,CAACkH,MAAM,CAACpH,KAAK,CAAE;oBAChDsH,QAAQ;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACF5K,OAAA,CAAChB,IAAI,CAAC2M,OAAO,CAACM,QAAQ;oBAACL,IAAI,EAAC,SAAS;oBAAAxB,QAAA,EAAC;kBAEtC;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN5K,OAAA,CAACb,GAAG;gBAACqM,EAAE,EAAE,CAAE;gBAAApB,QAAA,eACTpK,OAAA,CAAChB,IAAI,CAACyM,KAAK;kBAACP,SAAS,EAAC,MAAM;kBAAAd,QAAA,gBAC1BpK,OAAA,CAAChB,IAAI,CAAC0M,KAAK;oBAAAtB,QAAA,eAACpK,OAAA;sBAAAoK,QAAA,EAAQ;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrD5K,OAAA,CAAChB,IAAI,CAAC2M,OAAO;oBACXO,EAAE,EAAC,UAAU;oBACbC,IAAI,EAAE,CAAE;oBACRzH,KAAK,EAAE/D,WAAY;oBACnBkL,QAAQ,EAAGjH,CAAC,IAAKhE,cAAc,CAACgE,CAAC,CAACkH,MAAM,CAACpH,KAAK,CAAE;oBAChDqH,WAAW,EAAC;kBAA4C;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5K,OAAA;cAAKkL,SAAS,EAAC,gFAAgF;cAAAd,QAAA,gBAC7FpK,OAAA;gBAAIkL,SAAS,EAAC,MAAM;gBAAAd,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxC5K,OAAA,CAACf,MAAM;gBACL0G,OAAO,EAAC,SAAS;gBACjBwF,IAAI,EAAC,IAAI;gBACTiB,OAAO,EAAE3I,YAAa;gBACtB4I,QAAQ,EAAElK,YAAa;gBAAAiI,QAAA,gBAEvBpK,OAAA,CAACP,MAAM;kBAACyL,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAC7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELjH,OAAO,CAACyB,GAAG,CAAC,CAACd,MAAM,EAAET,KAAK,KAAK;cAAA,IAAAyI,qBAAA,EAAAC,sBAAA;cAC9B;cACA,MAAMC,cAAc,GAAGjM,iBAAiB,CAACqF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5E,EAAE,KAAKqD,MAAM,CAAChD,cAAc,CAAC;cAClF,MAAMmL,YAAY,GAAGD,cAAc,GAC/B;gBACE9H,KAAK,EAAE8H,cAAc,CAACvL,EAAE;gBACxB4J,KAAK,EAAE,GAAG,EAAAyB,qBAAA,GAAAE,cAAc,CAAC5F,sBAAsB,cAAA0F,qBAAA,uBAArCA,qBAAA,CAAuCzF,WAAW,KAAI,SAAS,MAAM2F,cAAc,CAACvG,UAAU,IAAIuG,cAAc,CAACtG,KAAK,KAAKsG,cAAc,CAACzG,cAAc,IAAIyG,cAAc,CAACxG,UAAU,mBAAmB;gBAClNE,KAAK,EAAEsG,cAAc,CAACtG,KAAK;gBAC3BH,cAAc,EAAEyG,cAAc,CAACzG,cAAc,IAAIyG,cAAc,CAACxG,UAAU;gBAC1EA,UAAU,EAAEwG,cAAc,CAACxG,UAAU;gBACrCa,WAAW,EAAE,EAAA0F,sBAAA,GAAAC,cAAc,CAAC5F,sBAAsB,cAAA2F,sBAAA,uBAArCA,sBAAA,CAAuC1F,WAAW,KAAI;cACrE,CAAC,GACD,IAAI;;cAER;cACA,MAAM6F,cAAc,GAAGnM,iBAAiB,CAAC6E,GAAG,CAAEO,OAAO,IAAK;gBAAA,IAAAgH,sBAAA,EAAAC,sBAAA;gBACxD;gBACA,MAAMC,iBAAiB,GAAG3I,wBAAwB,CAACyB,OAAO,CAAC1E,EAAE,EAAE4C,KAAK,CAAC;gBAErE,OAAO;kBACLa,KAAK,EAAEiB,OAAO,CAAC1E,EAAE;kBACjB4J,KAAK,EAAE,GAAG,EAAA8B,sBAAA,GAAAhH,OAAO,CAACiB,sBAAsB,cAAA+F,sBAAA,uBAA9BA,sBAAA,CAAgC9F,WAAW,KAAI,SAAS,MAAMlB,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACO,KAAK,KAAKP,OAAO,CAACI,cAAc,IAAIJ,OAAO,CAACK,UAAU,oBAAoB6G,iBAAiB,GAAG,qBAAqB,GAAG,EAAE,EAAE;kBAChO3G,KAAK,EAAEP,OAAO,CAACO,KAAK;kBACpBH,cAAc,EAAEJ,OAAO,CAACI,cAAc,IAAIJ,OAAO,CAACK,UAAU;kBAC5DA,UAAU,EAAEL,OAAO,CAACK,UAAU;kBAC9Ba,WAAW,EAAE,EAAA+F,sBAAA,GAAAjH,OAAO,CAACiB,sBAAsB,cAAAgG,sBAAA,uBAA9BA,sBAAA,CAAgC/F,WAAW,KAAI,SAAS;kBACrEiG,UAAU,EAAED,iBAAiB,CAAC;gBAChC,CAAC;cACH,CAAC,CAAC;cAEF,oBACE7M,OAAA,CAACjB,IAAI;gBAAamM,SAAS,EAAC,aAAa;gBAAAd,QAAA,gBACvCpK,OAAA,CAACjB,IAAI,CAACgO,MAAM;kBAAC7B,SAAS,EAAC,4DAA4D;kBAAAd,QAAA,gBACjFpK,OAAA;oBAAIkL,SAAS,EAAC,MAAM;oBAAAd,QAAA,GAAC,UAAQ,EAACvG,KAAK,GAAG,CAAC;kBAAA;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7C5K,OAAA,CAACf,MAAM;oBACL0G,OAAO,EAAC,gBAAgB;oBACxBwF,IAAI,EAAC,IAAI;oBACTiB,OAAO,EAAEA,CAAA,KAAMxI,eAAe,CAACC,KAAK,CAAE;oBACtCwI,QAAQ,EAAE1I,OAAO,CAAC6B,MAAM,KAAK,CAAE;oBAAA4E,QAAA,gBAE/BpK,OAAA,CAACN,OAAO;sBAACwL,SAAS,EAAC;oBAAM;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,WAC9B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACd5K,OAAA,CAACjB,IAAI,CAACsM,IAAI;kBAAAjB,QAAA,gBACRpK,OAAA,CAACd,GAAG;oBAAAkL,QAAA,gBACFpK,OAAA,CAACb,GAAG;sBAACqM,EAAE,EAAE,CAAE;sBAAApB,QAAA,eACTpK,OAAA,CAAChB,IAAI,CAACyM,KAAK;wBAACP,SAAS,EAAC,MAAM;wBAAAd,QAAA,gBAC1BpK,OAAA,CAAChB,IAAI,CAAC0M,KAAK;0BAAAtB,QAAA,eAACpK,OAAA;4BAAAoK,QAAA,EAAQ;0BAAsB;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,EAC/D/I,eAAe,gBACd7B,OAAA;0BAAKkL,SAAS,EAAC,2BAA2B;0BAAAd,QAAA,gBACxCpK,OAAA,CAACZ,OAAO;4BAAC4N,SAAS,EAAC,QAAQ;4BAAC7B,IAAI,EAAC,IAAI;4BAACD,SAAS,EAAC;0BAAM;4BAAAT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACzD5K,OAAA;4BAAAoK,QAAA,EAAM;0BAAmB;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7B,CAAC,gBAEN5K,OAAA,CAAAE,SAAA;0BAAAkK,QAAA,gBACEpK,OAAA,CAACnB,MAAM;4BACLoO,OAAO,EAAEP,cAAe;4BACxBQ,UAAU,EAAE;8BAAEC,MAAM,EAAEvD;4BAAa,CAAE;4BACrClF,KAAK,EAAE+H,YAAa;4BACpBZ,QAAQ,EAAGuB,cAAc,IAAK;8BAC5B5I,kBAAkB,CAACX,KAAK,EAAE,gBAAgB,EAAEuJ,cAAc,CAAC1I,KAAK,CAAC;4BACnE,CAAE;4BACFqH,WAAW,EAAC,uBAAuB;4BACnCsB,MAAM,EAAE;8BACNC,OAAO,EAAGC,QAAQ,KAAM;gCACtB,GAAGA,QAAQ;gCACXC,WAAW,EAAE,MAAM;gCACnBC,SAAS,EAAE,MAAM;gCACjBpD,MAAM,EAAE,MAAM;gCACd,SAAS,EAAE;kCACTmD,WAAW,EAAE;gCACf;8BACF,CAAC,CAAC;8BACFE,cAAc,EAAGH,QAAQ,KAAM;gCAC7B,GAAGA,QAAQ;gCACXlD,MAAM,EAAE,MAAM;gCACdF,OAAO,EAAE;8BACX,CAAC,CAAC;8BACFwD,MAAM,EAAEA,CAACJ,QAAQ,EAAEK,KAAK,MAAM;gCAC5B,GAAGL,QAAQ;gCACXjD,eAAe,EAAEsD,KAAK,CAACd,UAAU,GAC7B,SAAS,GACTc,KAAK,CAACC,UAAU,GACd,SAAS,GACTD,KAAK,CAACE,SAAS,GACb,SAAS,GACT,OAAO;gCACf5H,KAAK,EAAE0H,KAAK,CAACd,UAAU,GACnB,SAAS,GACTc,KAAK,CAACC,UAAU,GACd,OAAO,GACP,OAAO;gCACbE,MAAM,EAAEH,KAAK,CAACd,UAAU,GAAG,aAAa,GAAG;8BAC7C,CAAC;4BACH;0BAAE;4BAAArC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,EACD,CAACtG,MAAM,CAAChD,cAAc,IAAImB,SAAS,iBAClCzC,OAAA;4BAAKkL,SAAS,EAAC,wBAAwB;4BAAAd,QAAA,EAAC;0BAExC;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACN;wBAAA,eACD,CACH;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACN5K,OAAA,CAACb,GAAG;sBAACqM,EAAE,EAAE,CAAE;sBAAApB,QAAA,eACTpK,OAAA,CAAChB,IAAI,CAACyM,KAAK;wBAACP,SAAS,EAAC,MAAM;wBAAAd,QAAA,gBAC1BpK,OAAA;0BAAKkL,SAAS,EAAC,wDAAwD;0BAAAd,QAAA,gBACrEpK,OAAA,CAAChB,IAAI,CAAC0M,KAAK;4BAACR,SAAS,EAAC,MAAM;4BAAAd,QAAA,eAACpK,OAAA;8BAAAoK,QAAA,EAAQ;4BAAU;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,EACpE4B,cAAc,iBACbxM,OAAA;4BAAMkL,SAAS,EAAEpF,UAAU,CAACxB,MAAM,CAAC/C,UAAU,CAAC,IAAIiL,cAAc,CAACzG,cAAc,IAAIyG,cAAc,CAACxG,UAAU,CAAC,GAAG,mBAAmB,GAAG,oBAAqB;4BAAAoE,QAAA,GAAC,aAC/I,EAACoC,cAAc,CAACzG,cAAc,IAAIyG,cAAc,CAACxG,UAAU,EAAC,QACzE;0BAAA;4BAAAyE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACP;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACN5K,OAAA,CAAChB,IAAI,CAAC2M,OAAO;0BACXC,IAAI,EAAC,QAAQ;0BACboC,IAAI,EAAC,MAAM;0BACXC,GAAG,EAAC,GAAG;0BACPvJ,KAAK,EAAEJ,MAAM,CAAC/C,UAAW;0BACzBsK,QAAQ,EAAGjH,CAAC,IAAKJ,kBAAkB,CAACX,KAAK,EAAE,YAAY,EAAEe,CAAC,CAACkH,MAAM,CAACpH,KAAK,CAAE;0BACzEsH,QAAQ;0BACRD,WAAW,EAAC,kBAAkB;0BAC9BmC,SAAS,EAAE1B,cAAc,IAAI1G,UAAU,CAACxB,MAAM,CAAC/C,UAAU,CAAC,IAAIiL,cAAc,CAACzG,cAAc,IAAIyG,cAAc,CAACxG,UAAU,CAAE;0BAC1HkF,SAAS,EAAEsB,cAAc,IAAI1G,UAAU,CAACxB,MAAM,CAAC/C,UAAU,CAAC,IAAIiL,cAAc,CAACzG,cAAc,IAAIyG,cAAc,CAACxG,UAAU,CAAC,GAAG,eAAe,GAAG,EAAG;0BACjJgE,KAAK,EAAE;4BAAEK,MAAM,EAAE;0BAAO;wBAAE;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC,eACF5K,OAAA,CAAChB,IAAI,CAAC2M,OAAO,CAACM,QAAQ;0BAACL,IAAI,EAAC,SAAS;0BAAAxB,QAAA,EAClCoC,cAAc,IAAI1G,UAAU,CAACxB,MAAM,CAAC/C,UAAU,CAAC,IAAIiL,cAAc,CAACzG,cAAc,IAAIyG,cAAc,CAACxG,UAAU,CAAC,GAC3G,4BAA4BwG,cAAc,CAACzG,cAAc,IAAIyG,cAAc,CAACxG,UAAU,mBAAmB,GACzG;wBAAgC;0BAAAyE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN5K,OAAA,CAAChB,IAAI,CAAC0M,KAAK;oBAACR,SAAS,EAAC,MAAM;oBAAAd,QAAA,eAACpK,OAAA;sBAAAoK,QAAA,EAAQ;oBAAe;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC1E5K,OAAA,CAACd,GAAG;oBAAAkL,QAAA,GACD,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAChF,GAAG,CAAC,CAAC+F,IAAI,EAAEgD,SAAS,KAAK;sBACpD,MAAMC,OAAO,GAAGjD,IAAI,CAACkD,WAAW,CAAC,CAAC;sBAClC,oBACErO,OAAA,CAACb,GAAG;wBAAiBqC,EAAE,EAAE,CAAE;wBAAC8M,EAAE,EAAE,CAAE;wBAAC9C,EAAE,EAAE,CAAE;wBAACN,SAAS,EAAC,MAAM;wBAAAd,QAAA,eACxDpK,OAAA,CAAChB,IAAI,CAACyM,KAAK;0BAAArB,QAAA,gBACTpK,OAAA,CAAChB,IAAI,CAAC0M,KAAK;4BAACR,SAAS,EAAC,qBAAqB;4BAAAd,QAAA,EAAEe;0BAAI;4BAAAV,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAa,CAAC,eAC/D5K,OAAA,CAAChB,IAAI,CAAC2M,OAAO;4BACXC,IAAI,EAAC,QAAQ;4BACbqC,GAAG,EAAC,GAAG;4BACPvJ,KAAK,EAAEJ,MAAM,CAAC8J,OAAO,CAAE;4BACvBvC,QAAQ,EAAGjH,CAAC,IAAK;8BACf,MAAM2J,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAElF,QAAQ,CAAC3E,CAAC,CAACkH,MAAM,CAACpH,KAAK,IAAI,CAAC,CAAC,CAAC;8BACtDF,kBAAkB,CAACX,KAAK,EAAEuK,OAAO,EAAEG,GAAG,CAAC;4BACzC,CAAE;4BACFrD,SAAS,EAAC;0BAAa;4BAAAT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC,GAbLuD,SAAS;wBAAA1D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAcd,CAAC;oBAEV,CAAC,CAAC,eACF5K,OAAA,CAACb,GAAG;sBAACqC,EAAE,EAAE,CAAE;sBAAC8M,EAAE,EAAE,CAAE;sBAAC9C,EAAE,EAAE,CAAE;sBAACN,SAAS,EAAC,MAAM;sBAAAd,QAAA,eACxCpK,OAAA,CAAChB,IAAI,CAACyM,KAAK;wBAAArB,QAAA,gBACTpK,OAAA,CAAChB,IAAI,CAAC0M,KAAK;0BAACR,SAAS,EAAC,qBAAqB;0BAAAd,QAAA,EAAC;wBAAK;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC9D5K,OAAA;0BAAKkL,SAAS,EAAC,mCAAmC;0BAAAd,QAAA,EAC/Cb,QAAQ,CAACjF,MAAM,CAAC9C,EAAE,IAAI,CAAC,CAAC,GACxB+H,QAAQ,CAACjF,MAAM,CAAC7C,CAAC,IAAI,CAAC,CAAC,GACvB8H,QAAQ,CAACjF,MAAM,CAAC5C,CAAC,IAAI,CAAC,CAAC,GACvB6H,QAAQ,CAACjF,MAAM,CAAC3C,CAAC,IAAI,CAAC,CAAC,GACvB4H,QAAQ,CAACjF,MAAM,CAAC1C,EAAE,IAAI,CAAC;wBAAC;0BAAA6I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA,GA5IH/G,KAAK;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6IV,CAAC;YAEX,CAAC,CAAC,eAEF5K,OAAA;cAAKkL,SAAS,EAAC,iCAAiC;cAAAd,QAAA,eAC9CpK,OAAA,CAACjB,IAAI;gBAACmM,SAAS,EAAC,UAAU;gBAAClB,KAAK,EAAE;kBAAEM,eAAe,EAAE;gBAAU,CAAE;gBAAAF,QAAA,eAC/DpK,OAAA,CAACjB,IAAI,CAACsM,IAAI;kBAACH,SAAS,EAAC,MAAM;kBAAAd,QAAA,eACzBpK,OAAA;oBAAKkL,SAAS,EAAC,oBAAoB;oBAAAd,QAAA,gBACjCpK,OAAA;sBAAKkL,SAAS,EAAC,gCAAgC;sBAAAd,QAAA,gBAC7CpK,OAAA;wBAAQkL,SAAS,EAAC,MAAM;wBAAAd,QAAA,EAAC;sBAAiB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnD5K,OAAA,CAACV,KAAK;wBAACoP,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,MAAI,EAACjD,eAAe,CAAC3F,EAAE;sBAAA;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACrE5K,OAAA,CAACV,KAAK;wBAACoP,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,KAAG,EAACjD,eAAe,CAAC1F,CAAC;sBAAA;wBAAAgJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnE5K,OAAA,CAACV,KAAK;wBAACoP,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,KAAG,EAACjD,eAAe,CAACzF,CAAC;sBAAA;wBAAA+I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnE5K,OAAA,CAACV,KAAK;wBAACoP,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,KAAG,EAACjD,eAAe,CAACxF,CAAC;sBAAA;wBAAA8I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnE5K,OAAA,CAACV,KAAK;wBAACoP,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,MAAI,EAACjD,eAAe,CAACvF,EAAE;sBAAA;wBAAA6I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACrE5K,OAAA,CAACV,KAAK;wBAACoP,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,SAAO,EAACjD,eAAe,CAACmC,KAAK;sBAAA;wBAAAmB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC,eACN5K,OAAA;sBAAKkL,SAAS,EAAC,2BAA2B;sBAAAd,QAAA,gBACxCpK,OAAA;wBAAQkL,SAAS,EAAC,MAAM;wBAAAd,QAAA,EAAC;sBAAiB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnD5K,OAAA,CAACV,KAAK;wBAACoP,EAAE,EAAC,MAAM;wBAAAtE,QAAA,GAAEjD,eAAe,CAAC5F,UAAU,CAACiI,OAAO,CAAC,CAAC,CAAC,EAAC,QAAM;sBAAA;wBAAAiB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN5K,OAAA;cAAKkL,SAAS,EAAC,oCAAoC;cAAAd,QAAA,eACjDpK,OAAA,CAACf,MAAM;gBACL2M,IAAI,EAAC,QAAQ;gBACbjG,OAAO,EAAC,SAAS;gBACjBwF,IAAI,EAAC,IAAI;gBACTkB,QAAQ,EAAElK,YAAa;gBACvB+I,SAAS,EAAC,MAAM;gBAAAd,QAAA,EAEfjI,YAAY,gBACXnC,OAAA,CAAAE,SAAA;kBAAAkK,QAAA,gBACEpK,OAAA,CAACZ,OAAO;oBAAC8M,EAAE,EAAC,MAAM;oBAACc,SAAS,EAAC,QAAQ;oBAAC7B,IAAI,EAAC,IAAI;oBAACwD,IAAI,EAAC,QAAQ;oBAAC,eAAY,MAAM;oBAACzD,SAAS,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAEtG;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN5K,OAAA,CAACT,KAAK;MAACqP,IAAI,EAAEjM,YAAa;MAACkM,MAAM,EAAElF,gBAAiB;MAACwB,IAAI,EAAC,IAAI;MAAC2D,QAAQ;MAAA1E,QAAA,gBACrEpK,OAAA,CAACT,KAAK,CAACwN,MAAM;QAACgC,WAAW;QAAA3E,QAAA,eACvBpK,OAAA,CAACT,KAAK,CAACyP,KAAK;UAAA5E,QAAA,EAAC;QAA2B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACf5K,OAAA,CAACT,KAAK,CAAC8L,IAAI;QAAAjB,QAAA,gBACTpK,OAAA;UAAAoK,QAAA,EAAG;QAAyD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAE/D/H,eAAe,iBACd7C,OAAA;UAAKkL,SAAS,EAAC,MAAM;UAAAd,QAAA,gBACnBpK,OAAA;YAAAoK,QAAA,EAAG;UAA+C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtD5K,OAAA;YAAAoK,QAAA,gBACEpK,OAAA;cAAAoK,QAAA,gBAAIpK,OAAA;gBAAAoK,QAAA,EAAQ;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC/H,eAAe,CAACwD,YAAY;YAAA;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtE5K,OAAA;cAAAoK,QAAA,gBAAIpK,OAAA;gBAAAoK,QAAA,EAAQ;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC/H,eAAe,CAACgE,WAAW;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/D5K,OAAA;cAAAoK,QAAA,gBAAIpK,OAAA;gBAAAoK,QAAA,EAAQ;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,IAAI1J,IAAI,CAAC2B,eAAe,CAACuD,YAAY,CAAC,CAACiC,kBAAkB,CAAC,CAAC;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrG5K,OAAA;cAAAoK,QAAA,gBAAIpK,OAAA;gBAAAoK,QAAA,EAAQ;cAAiB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,SAAK,EAAC/H,eAAe,CAACsE,eAAe,CAAC3F,EAAE,EAAC,OAC1E,EAACqB,eAAe,CAACsE,eAAe,CAAC1F,CAAC,EAAC,OACnC,EAACoB,eAAe,CAACsE,eAAe,CAACzF,CAAC,EAAC,OACnC,EAACmB,eAAe,CAACsE,eAAe,CAACxF,CAAC,EAAC,QAClC,EAACkB,eAAe,CAACsE,eAAe,CAACvF,EAAE;YAAA;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/C5K,OAAA;cAAAoK,QAAA,gBAAIpK,OAAA;gBAAAoK,QAAA,EAAQ;cAAY;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC/H,eAAe,CAACsE,eAAe,CAACmC,KAAK;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9E5K,OAAA;cAAAoK,QAAA,gBAAIpK,OAAA;gBAAAoK,QAAA,EAAQ;cAAiB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC/H,eAAe,CAACsE,eAAe,CAAC5F,UAAU,CAACiI,OAAO,CAAC,CAAC,CAAC,EAAC,QAAM;YAAA;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACb5K,OAAA,CAACT,KAAK,CAAC0P,MAAM;QAAA7E,QAAA,gBACXpK,OAAA,CAACf,MAAM;UAAC0G,OAAO,EAAC,WAAW;UAACyG,OAAO,EAAEzC,gBAAiB;UAAAS,QAAA,EAAC;QAEvD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5K,OAAA,CAACf,MAAM;UAAC0G,OAAO,EAAC,SAAS;UAACyG,OAAO,EAAE/E,WAAY;UAAA+C,QAAA,gBAC7CpK,OAAA,CAACH,SAAS;YAACqL,SAAS,EAAC;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAChC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACxK,EAAA,CAvuBID,gBAAgB;AAAA+O,EAAA,GAAhB/O,gBAAgB;AAyuBtB,eAAeA,gBAAgB;AAAC,IAAA+O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}