{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\System Development Project\\\\pri new\\\\frontend\\\\src\\\\pages\\\\AddCutting.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Select from 'react-select';\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\nimport { Card, Form, Button, Row, Col, Spinner, Alert, Container, Badge, Modal } from 'react-bootstrap';\nimport { BsScissors, BsPlus, BsTrash, BsCheck2Circle, BsExclamationTriangle, BsFilePdf } from 'react-icons/bs';\nimport jsPDF from 'jspdf';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AddCuttingRecord = () => {\n  _s();\n  // Overall cutting record fields\n  const [allFabricVariants, setAllFabricVariants] = useState([]);\n  const [cuttingDate, setCuttingDate] = useState('');\n  const [description, setDescription] = useState('');\n  const [productName, setProductName] = useState('');\n\n  // Cutting detail rows\n  const [details, setDetails] = useState([{\n    fabric_variant: '',\n    yard_usage: '',\n    xs: 0,\n    s: 0,\n    m: 0,\n    l: 0,\n    xl: 0\n  }]);\n\n  // Loading, error, success states\n  const [loadingVariants, setLoadingVariants] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\n  const [validated, setValidated] = useState(false);\n  const [showPdfModal, setShowPdfModal] = useState(false);\n  const [submittedRecord, setSubmittedRecord] = useState(null);\n\n  // Add resize event listener to update sidebar state\n  useEffect(() => {\n    const handleResize = () => {\n      setIsSidebarOpen(window.innerWidth >= 768);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n\n  // Fetch all fabric variants on mount\n  useEffect(() => {\n    setLoadingVariants(true);\n    axios.get(\"http://localhost:8000/api/fabric-variants/\").then(res => {\n      setAllFabricVariants(res.data);\n      setLoadingVariants(false);\n    }).catch(err => {\n      console.error('Error fetching fabric variants:', err);\n      setError('Failed to load fabric variants. Please try again.');\n      setLoadingVariants(false);\n    });\n  }, []);\n\n  // Add a new empty detail row\n  const addDetailRow = () => {\n    setDetails([...details, {\n      fabric_variant: '',\n      yard_usage: '',\n      xs: 0,\n      s: 0,\n      m: 0,\n      l: 0,\n      xl: 0\n    }]);\n  };\n\n  // Delete a detail row\n  const removeDetailRow = index => {\n    const newDetails = details.filter((_, i) => i !== index);\n    setDetails(newDetails);\n  };\n\n  // Check if a fabric variant is already selected in another detail row\n  const isDuplicateFabricVariant = (variantId, currentIndex) => {\n    return details.some((detail, idx) => idx !== currentIndex && detail.fabric_variant === variantId && variantId !== '');\n  };\n\n  // Handle change for each detail row field\n  const handleDetailChange = (index, field, value) => {\n    const newDetails = [...details];\n\n    // If changing fabric variant, check for duplicates\n    if (field === 'fabric_variant') {\n      if (isDuplicateFabricVariant(value, index)) {\n        setError(`This fabric variant is already selected in another detail. Please select a different variant.`);\n        return; // Don't update the state if duplicate\n      } else {\n        setError(''); // Clear error if no duplicate\n      }\n    }\n    newDetails[index][field] = value;\n    setDetails(newDetails);\n  };\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Form validation\n    const form = e.currentTarget;\n    if (form.checkValidity() === false) {\n      e.stopPropagation();\n      setValidated(true);\n      return;\n    }\n\n    // Check if any detail has a fabric variant selected\n    const hasValidDetails = details.some(detail => detail.fabric_variant);\n    if (!hasValidDetails) {\n      setError('Please select at least one fabric variant for your cutting details.');\n      return;\n    }\n\n    // Check for duplicate fabric variants\n    const selectedVariants = details.map(detail => detail.fabric_variant).filter(Boolean);\n    const uniqueVariants = [...new Set(selectedVariants)];\n    if (selectedVariants.length !== uniqueVariants.length) {\n      setError('You have selected the same fabric variant in multiple details. Please use unique fabric variants for each detail.');\n      setValidated(true);\n      return;\n    }\n\n    // Validate yard availability for each detail\n    let yardValidationError = false;\n    details.forEach(detail => {\n      if (detail.fabric_variant) {\n        const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\n        if (variant && parseFloat(detail.yard_usage) > (variant.available_yard || variant.total_yard)) {\n          yardValidationError = true;\n          setError(`Yard usage for ${variant.color_name || variant.color} exceeds available yards (${variant.available_yard || variant.total_yard} yards available).`);\n        }\n      }\n    });\n    if (yardValidationError) {\n      setValidated(true);\n      return;\n    }\n    setValidated(true);\n    setIsSubmitting(true);\n    setError('');\n    setSuccess('');\n    const payload = {\n      cutting_date: cuttingDate,\n      description: description,\n      product_name: productName,\n      details: details\n    };\n    try {\n      const response = await axios.post(\"http://localhost:8000/api/cutting/cutting-records/\", payload);\n      setSuccess('Cutting record created successfully!');\n\n      // Store the submitted record for PDF generation\n      const fabricNames = new Set();\n      const recordData = {\n        ...response.data,\n        details: response.data.details.map(detail => {\n          var _variant$fabric_defin, _variant$fabric_defin2;\n          const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\n          if (variant !== null && variant !== void 0 && (_variant$fabric_defin = variant.fabric_definition_data) !== null && _variant$fabric_defin !== void 0 && _variant$fabric_defin.fabric_name) {\n            fabricNames.add(variant.fabric_definition_data.fabric_name);\n          }\n          return {\n            ...detail,\n            color: (variant === null || variant === void 0 ? void 0 : variant.color) || 'Unknown',\n            color_name: (variant === null || variant === void 0 ? void 0 : variant.color_name) || (variant === null || variant === void 0 ? void 0 : variant.color) || 'Unknown',\n            fabric_name: (variant === null || variant === void 0 ? void 0 : (_variant$fabric_defin2 = variant.fabric_definition_data) === null || _variant$fabric_defin2 === void 0 ? void 0 : _variant$fabric_defin2.fabric_name) || 'Unknown'\n          };\n        }),\n        fabric_names: Array.from(fabricNames).join(', ') || 'Unknown',\n        totalQuantities: totalQuantities\n      };\n      setSubmittedRecord(recordData);\n\n      // Show the PDF generation modal\n      setShowPdfModal(true);\n    } catch (err) {\n      console.error('Error creating cutting record:', err);\n      if (err.response && err.response.data) {\n        // Display more specific error message if available\n        const errorMessage = typeof err.response.data === 'string' ? err.response.data : 'Failed to create cutting record. Please check your inputs.';\n        setError(errorMessage);\n      } else {\n        setError('Failed to create cutting record. Please try again.');\n      }\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  // Function to generate PDF directly without using html2canvas\n  const generatePDF = () => {\n    if (!submittedRecord) return;\n    try {\n      // Create a new PDF document\n      const pdf = new jsPDF({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n      });\n\n      // Set font sizes and styles\n      const titleFontSize = 18;\n      const headingFontSize = 14;\n      const normalFontSize = 10;\n      const smallFontSize = 8;\n\n      // Add title\n      pdf.setFontSize(titleFontSize);\n      pdf.setFont('helvetica', 'bold');\n      pdf.text('Cutting Record', 105, 20, {\n        align: 'center'\n      });\n\n      // Add general information section\n      pdf.setFontSize(headingFontSize);\n      pdf.text('General Information', 20, 35);\n      pdf.setFontSize(normalFontSize);\n      pdf.setFont('helvetica', 'normal');\n\n      // Draw table for general info\n      pdf.line(20, 40, 190, 40); // Top horizontal line\n\n      const generalInfoData = [['Record ID', submittedRecord.id.toString()], ['Product Name', submittedRecord.product_name], ['Fabrics Used', submittedRecord.fabric_names], ['Cutting Date', new Date(submittedRecord.cutting_date).toLocaleDateString()], ['Description', submittedRecord.description || 'N/A']];\n      let yPos = 45;\n      generalInfoData.forEach(row => {\n        pdf.setFont('helvetica', 'bold');\n        pdf.text(row[0], 25, yPos);\n        pdf.setFont('helvetica', 'normal');\n        pdf.text(row[1], 80, yPos);\n        yPos += 8;\n        pdf.line(20, yPos - 3, 190, yPos - 3); // Horizontal line after each row\n      });\n\n      // Add fabric details section\n      pdf.setFontSize(headingFontSize);\n      pdf.setFont('helvetica', 'bold');\n      pdf.text('Fabric Details', 20, yPos + 10);\n\n      // Table headers for fabric details\n      const headers = ['Fabric', 'Color', 'Yard Usage', 'XS', 'S', 'M', 'L', 'XL', 'Total'];\n      const colWidths = [35, 35, 20, 12, 12, 12, 12, 12, 15];\n\n      // Calculate starting positions for each column\n      const colPositions = [];\n      let currentPos = 20;\n      colWidths.forEach(width => {\n        colPositions.push(currentPos);\n        currentPos += width;\n      });\n\n      // Draw table header\n      yPos += 15;\n      pdf.setFontSize(normalFontSize);\n      pdf.setFont('helvetica', 'bold');\n\n      // Draw header background\n      pdf.setFillColor(240, 240, 240);\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\n\n      // Draw header text\n      headers.forEach((header, index) => {\n        pdf.text(header, colPositions[index] + 2, yPos);\n      });\n\n      // Draw horizontal line after header\n      yPos += 3;\n      pdf.line(20, yPos, 190, yPos);\n\n      // Draw table rows\n      pdf.setFont('helvetica', 'normal');\n      submittedRecord.details.forEach(detail => {\n        var _detail$xs, _detail$s, _detail$m, _detail$l, _detail$xl;\n        yPos += 8;\n\n        // Calculate total for this row\n        const total = parseInt(detail.xs || 0) + parseInt(detail.s || 0) + parseInt(detail.m || 0) + parseInt(detail.l || 0) + parseInt(detail.xl || 0);\n\n        // Draw row data\n        pdf.text(detail.fabric_name || 'Unknown', colPositions[0] + 2, yPos);\n        pdf.text(detail.color_name || detail.color, colPositions[1] + 2, yPos);\n        pdf.text(`${detail.yard_usage} yards`, colPositions[2] + 2, yPos);\n        pdf.text(((_detail$xs = detail.xs) === null || _detail$xs === void 0 ? void 0 : _detail$xs.toString()) || '0', colPositions[3] + 2, yPos);\n        pdf.text(((_detail$s = detail.s) === null || _detail$s === void 0 ? void 0 : _detail$s.toString()) || '0', colPositions[4] + 2, yPos);\n        pdf.text(((_detail$m = detail.m) === null || _detail$m === void 0 ? void 0 : _detail$m.toString()) || '0', colPositions[5] + 2, yPos);\n        pdf.text(((_detail$l = detail.l) === null || _detail$l === void 0 ? void 0 : _detail$l.toString()) || '0', colPositions[6] + 2, yPos);\n        pdf.text(((_detail$xl = detail.xl) === null || _detail$xl === void 0 ? void 0 : _detail$xl.toString()) || '0', colPositions[7] + 2, yPos);\n        pdf.text(total.toString(), colPositions[8] + 2, yPos);\n\n        // Draw horizontal line after row\n        yPos += 3;\n        pdf.line(20, yPos, 190, yPos);\n      });\n\n      // Draw totals row\n      yPos += 8;\n      pdf.setFillColor(240, 240, 240);\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\n      pdf.setFont('helvetica', 'bold');\n      pdf.text('Total', colPositions[0] + 2, yPos);\n      pdf.text('', colPositions[1] + 2, yPos);\n      pdf.text(`${submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards`, colPositions[2] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.xs.toString(), colPositions[3] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.s.toString(), colPositions[4] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.m.toString(), colPositions[5] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.l.toString(), colPositions[6] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.xl.toString(), colPositions[7] + 2, yPos);\n      pdf.text(submittedRecord.totalQuantities.total.toString(), colPositions[8] + 2, yPos);\n\n      // Add footer\n      pdf.setFontSize(smallFontSize);\n      pdf.setFont('helvetica', 'italic');\n      pdf.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, {\n        align: 'center'\n      });\n      pdf.text('Fashion Garment Management System', 105, 285, {\n        align: 'center'\n      });\n\n      // Save the PDF\n      pdf.save(`Cutting_Record_${submittedRecord.id}_${submittedRecord.product_name}.pdf`);\n\n      // Reset form after PDF generation\n      setShowPdfModal(false);\n      setCuttingDate('');\n      setDescription('');\n      setProductName('');\n      setDetails([{\n        fabric_variant: '',\n        yard_usage: '',\n        xs: 0,\n        s: 0,\n        m: 0,\n        l: 0,\n        xl: 0\n      }]);\n      setValidated(false);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      setError('Failed to generate PDF. Please try again.');\n      setShowPdfModal(false);\n    }\n  };\n\n  // Function to handle modal close without generating PDF\n  const handleCloseModal = () => {\n    setShowPdfModal(false);\n    // Reset form\n    setCuttingDate('');\n    setDescription('');\n    setProductName('');\n    setDetails([{\n      fabric_variant: '',\n      yard_usage: '',\n      xs: 0,\n      s: 0,\n      m: 0,\n      l: 0,\n      xl: 0\n    }]);\n    setValidated(false);\n  };\n\n  // Custom option component that shows a color swatch + label\n  const ColourOption = ({\n    data,\n    innerRef,\n    innerProps\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: innerRef,\n    ...innerProps,\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      padding: '4px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: 16,\n        height: 16,\n        backgroundColor: data.color,\n        marginRight: 8,\n        border: '1px solid #ccc'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      children: data.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 360,\n    columnNumber: 5\n  }, this);\n\n  // Calculate total quantities for all details\n  const totalQuantities = details.reduce((acc, detail) => {\n    acc.xs += parseInt(detail.xs) || 0;\n    acc.s += parseInt(detail.s) || 0;\n    acc.m += parseInt(detail.m) || 0;\n    acc.l += parseInt(detail.l) || 0;\n    acc.xl += parseInt(detail.xl) || 0;\n    acc.total += (parseInt(detail.xs) || 0) + (parseInt(detail.s) || 0) + (parseInt(detail.m) || 0) + (parseInt(detail.l) || 0) + (parseInt(detail.xl) || 0);\n    acc.yard_usage += parseFloat(detail.yard_usage) || 0;\n    return acc;\n  }, {\n    xs: 0,\n    s: 0,\n    m: 0,\n    l: 0,\n    xl: 0,\n    total: 0,\n    yard_usage: 0\n  });\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedNavBar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\n        width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\n        transition: \"all 0.3s ease\",\n        padding: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(BsScissors, {\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), \"Add Cutting Record\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"success\",\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(BsCheck2Circle, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 13\n        }, this), success]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        variant: \"danger\",\n        className: \"d-flex align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(BsExclamationTriangle, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this), error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"mb-4 shadow-sm\",\n        style: {\n          backgroundColor: \"#D9EDFB\",\n          borderRadius: \"10px\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            noValidate: true,\n            validated: validated,\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              children: /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Product Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"text\",\n                    value: productName,\n                    onChange: e => setProductName(e.target.value),\n                    placeholder: \"Enter product name\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                    type: \"invalid\",\n                    children: \"Please provide a product name.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Cutting Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 451,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"date\",\n                    value: cuttingDate,\n                    onChange: e => setCuttingDate(e.target.value),\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 452,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                    type: \"invalid\",\n                    children: \"Please select a cutting date.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 33\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    as: \"textarea\",\n                    rows: 3,\n                    value: description,\n                    onChange: e => setDescription(e.target.value),\n                    placeholder: \"Enter details about this cutting record...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-0\",\n                children: \"Fabric Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"success\",\n                size: \"sm\",\n                onClick: addDetailRow,\n                disabled: isSubmitting,\n                children: [/*#__PURE__*/_jsxDEV(BsPlus, {\n                  className: \"me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), \" Add Fabric Variant\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this), details.map((detail, index) => {\n              var _currentVariant$fabri, _currentVariant$fabri2;\n              // Find the selected variant object to set the value in React-Select\n              const currentVariant = allFabricVariants.find(v => v.id === detail.fabric_variant);\n              const currentValue = currentVariant ? {\n                value: currentVariant.id,\n                label: `${((_currentVariant$fabri = currentVariant.fabric_definition_data) === null || _currentVariant$fabri === void 0 ? void 0 : _currentVariant$fabri.fabric_name) || 'Unknown'} - ${currentVariant.color_name || currentVariant.color} (${currentVariant.available_yard || currentVariant.total_yard} yards available)`,\n                color: currentVariant.color,\n                available_yard: currentVariant.available_yard || currentVariant.total_yard,\n                total_yard: currentVariant.total_yard,\n                fabric_name: ((_currentVariant$fabri2 = currentVariant.fabric_definition_data) === null || _currentVariant$fabri2 === void 0 ? void 0 : _currentVariant$fabri2.fabric_name) || 'Unknown'\n              } : null;\n\n              // Prepare the variant options for React-Select\n              const variantOptions = allFabricVariants.map(variant => {\n                var _variant$fabric_defin3, _variant$fabric_defin4;\n                // Check if this variant is already selected in another detail\n                const isAlreadySelected = isDuplicateFabricVariant(variant.id, index);\n                return {\n                  value: variant.id,\n                  label: `${((_variant$fabric_defin3 = variant.fabric_definition_data) === null || _variant$fabric_defin3 === void 0 ? void 0 : _variant$fabric_defin3.fabric_name) || 'Unknown'} - ${variant.color_name || variant.color} (${variant.available_yard || variant.total_yard} yards available)${isAlreadySelected ? ' - Already Selected' : ''}`,\n                  color: variant.color,\n                  available_yard: variant.available_yard || variant.total_yard,\n                  total_yard: variant.total_yard,\n                  fabric_name: ((_variant$fabric_defin4 = variant.fabric_definition_data) === null || _variant$fabric_defin4 === void 0 ? void 0 : _variant$fabric_defin4.fabric_name) || 'Unknown',\n                  isDisabled: isAlreadySelected // Disable options that are already selected\n                };\n              });\n              return /*#__PURE__*/_jsxDEV(Card, {\n                className: \"mb-3 border\",\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  className: \"d-flex justify-content-between align-items-center bg-light\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"mb-0\",\n                    children: [\"Detail #\", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-danger\",\n                    size: \"sm\",\n                    onClick: () => removeDetailRow(index),\n                    disabled: details.length === 1,\n                    children: [/*#__PURE__*/_jsxDEV(BsTrash, {\n                      className: \"me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 25\n                    }, this), \" Remove\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: [/*#__PURE__*/_jsxDEV(Row, {\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                            children: \"Fabric Variant (Color)\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 536,\n                            columnNumber: 41\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 536,\n                          columnNumber: 29\n                        }, this), loadingVariants ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex align-items-center\",\n                          children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                            animation: \"border\",\n                            size: \"sm\",\n                            className: \"me-2\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 539,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"Loading variants...\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 540,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 538,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Select, {\n                            options: variantOptions,\n                            components: {\n                              Option: ColourOption\n                            },\n                            value: currentValue,\n                            onChange: selectedOption => {\n                              handleDetailChange(index, 'fabric_variant', selectedOption.value);\n                            },\n                            placeholder: \"Select Fabric Variant\",\n                            styles: {\n                              control: provided => ({\n                                ...provided,\n                                borderColor: '#ddd',\n                                boxShadow: 'none',\n                                height: '38px',\n                                '&:hover': {\n                                  borderColor: '#aaa'\n                                }\n                              }),\n                              valueContainer: provided => ({\n                                ...provided,\n                                height: '38px',\n                                padding: '0 8px'\n                              }),\n                              option: (provided, state) => ({\n                                ...provided,\n                                backgroundColor: state.isDisabled ? '#f8f9fa' : state.isSelected ? '#007bff' : state.isFocused ? '#e9ecef' : 'white',\n                                color: state.isDisabled ? '#6c757d' : state.isSelected ? 'white' : 'black',\n                                cursor: state.isDisabled ? 'not-allowed' : 'default'\n                              })\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 544,\n                            columnNumber: 33\n                          }, this), !detail.fabric_variant && validated && /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-danger small mt-1\",\n                            children: \"Please select a fabric variant.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 586,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 535,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 534,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"d-flex justify-content-between align-items-center mb-1\",\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            className: \"mb-0\",\n                            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                              children: \"Yard Usage\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 597,\n                              columnNumber: 60\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 597,\n                            columnNumber: 31\n                          }, this), currentVariant && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"text-danger small\" : \"text-success small\",\n                            children: [\"Available: \", currentVariant.available_yard || currentVariant.total_yard, \" yards\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 599,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 596,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"number\",\n                          step: \"0.01\",\n                          min: \"0\",\n                          value: detail.yard_usage,\n                          onChange: e => handleDetailChange(index, 'yard_usage', e.target.value),\n                          required: true,\n                          placeholder: \"Enter yards used\",\n                          isInvalid: currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard),\n                          className: currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"border-danger\" : \"\",\n                          style: {\n                            height: '38px'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 604,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control.Feedback, {\n                          type: \"invalid\",\n                          children: currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? `Exceeds available yards (${currentVariant.available_yard || currentVariant.total_yard} yards available)` : \"Please enter valid yard usage.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 616,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Label, {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Size Quantities\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 625,\n                      columnNumber: 52\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 625,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    children: [[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size, sizeIndex) => {\n                      const sizeKey = size.toLowerCase();\n                      return /*#__PURE__*/_jsxDEV(Col, {\n                        xs: 6,\n                        sm: 4,\n                        md: 2,\n                        className: \"mb-3\",\n                        children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                            className: \"text-center d-block\",\n                            children: size\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 632,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"number\",\n                            min: \"0\",\n                            value: detail[sizeKey],\n                            onChange: e => {\n                              const val = Math.max(0, parseInt(e.target.value || 0));\n                              handleDetailChange(index, sizeKey, val);\n                            },\n                            className: \"text-center\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 633,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 631,\n                          columnNumber: 31\n                        }, this)\n                      }, sizeIndex, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 630,\n                        columnNumber: 29\n                      }, this);\n                    }), /*#__PURE__*/_jsxDEV(Col, {\n                      xs: 6,\n                      sm: 4,\n                      md: 2,\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          className: \"text-center d-block\",\n                          children: \"Total\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 649,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"form-control text-center bg-light\",\n                          children: parseInt(detail.xs || 0) + parseInt(detail.s || 0) + parseInt(detail.m || 0) + parseInt(detail.l || 0) + parseInt(detail.xl || 0)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 650,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 648,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 647,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-end mb-4\",\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"border-0\",\n                style: {\n                  backgroundColor: \"#e8f4fe\"\n                },\n                children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                  className: \"py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex flex-column\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"me-2\",\n                        children: \"Total Quantities:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 670,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"XS: \", totalQuantities.xs]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 671,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"S: \", totalQuantities.s]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 672,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"M: \", totalQuantities.m]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 673,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"L: \", totalQuantities.l]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 674,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"primary\",\n                        className: \"me-1\",\n                        children: [\"XL: \", totalQuantities.xl]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 675,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"success\",\n                        className: \"ms-2\",\n                        children: [\"Total: \", totalQuantities.total]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 676,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"me-2\",\n                        children: \"Total Yard Usage:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 679,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                        bg: \"info\",\n                        children: [totalQuantities.yard_usage.toFixed(2), \" yards\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 680,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 678,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-center mt-4\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"primary\",\n                size: \"lg\",\n                disabled: isSubmitting,\n                className: \"px-5\",\n                children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 697,\n                    columnNumber: 23\n                  }, this), \"Submitting...\"]\n                }, void 0, true) : 'Submit Cutting Record'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showPdfModal,\n      onHide: handleCloseModal,\n      size: \"lg\",\n      centered: true,\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: \"Generate Cutting Record PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 713,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 712,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Would you like to generate a PDF for this cutting record?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 716,\n          columnNumber: 11\n        }, this), submittedRecord && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"The PDF will include the following information:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Product Name:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.product_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Fabric:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.fabric_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Cutting Date:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 21\n              }, this), \" \", new Date(submittedRecord.cutting_date).toLocaleDateString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Quantities:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 21\n              }, this), \" XS: \", submittedRecord.totalQuantities.xs, \", S: \", submittedRecord.totalQuantities.s, \", M: \", submittedRecord.totalQuantities.m, \", L: \", submittedRecord.totalQuantities.l, \", XL: \", submittedRecord.totalQuantities.xl]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Items:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.totalQuantities.total]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Yard Usage:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 21\n              }, this), \" \", submittedRecord.totalQuantities.yard_usage.toFixed(2), \" yards\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 715,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: handleCloseModal,\n          children: \"No, Skip\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: generatePDF,\n          children: [/*#__PURE__*/_jsxDEV(BsFilePdf, {\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 13\n          }, this), \" Generate PDF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 736,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 711,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AddCuttingRecord, \"TysG+RPDvDD8cEYslRsPlAUVGXg=\");\n_c = AddCuttingRecord;\nexport default AddCuttingRecord;\nvar _c;\n$RefreshReg$(_c, \"AddCuttingRecord\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Select", "RoleBasedNavBar", "Card", "Form", "<PERSON><PERSON>", "Row", "Col", "Spinner", "<PERSON><PERSON>", "Container", "Badge", "Modal", "BsScissors", "BsPlus", "BsTrash", "BsCheck2Circle", "BsExclamationTriangle", "BsFilePdf", "jsPDF", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddCuttingRecord", "_s", "allFabricVariants", "setAllFabricVariants", "cuttingDate", "setCuttingDate", "description", "setDescription", "productName", "setProductName", "details", "setDetails", "fabric_variant", "yard_usage", "xs", "s", "m", "l", "xl", "loadingVariants", "setLoadingVariants", "error", "setError", "success", "setSuccess", "isSubmitting", "setIsSubmitting", "isSidebarOpen", "setIsSidebarOpen", "window", "innerWidth", "validated", "setValidated", "showPdfModal", "setShowPdfModal", "submittedRecord", "setSubmittedRecord", "handleResize", "addEventListener", "removeEventListener", "get", "then", "res", "data", "catch", "err", "console", "addDetailRow", "removeDetailRow", "index", "newDetails", "filter", "_", "i", "isDuplicateFabricVariant", "variantId", "currentIndex", "some", "detail", "idx", "handleDetailChange", "field", "value", "handleSubmit", "e", "preventDefault", "form", "currentTarget", "checkValidity", "stopPropagation", "hasValidDetails", "selectedVariants", "map", "Boolean", "uniqueVariants", "Set", "length", "yardValidationError", "for<PERSON>ach", "variant", "find", "v", "id", "parseFloat", "available_yard", "total_yard", "color_name", "color", "payload", "cutting_date", "product_name", "response", "post", "fabricNames", "recordData", "_variant$fabric_defin", "_variant$fabric_defin2", "fabric_definition_data", "fabric_name", "add", "fabric_names", "Array", "from", "join", "totalQuantities", "errorMessage", "generatePDF", "pdf", "orientation", "unit", "format", "titleFontSize", "headingFontSize", "normalFontSize", "smallFontSize", "setFontSize", "setFont", "text", "align", "line", "generalInfoData", "toString", "Date", "toLocaleDateString", "yPos", "row", "headers", "col<PERSON><PERSON><PERSON>", "colPositions", "currentPos", "width", "push", "setFillColor", "rect", "header", "_detail$xs", "_detail$s", "_detail$m", "_detail$l", "_detail$xl", "total", "parseInt", "toFixed", "toLocaleString", "save", "handleCloseModal", "ColourOption", "innerRef", "innerProps", "ref", "style", "display", "alignItems", "padding", "children", "height", "backgroundColor", "marginRight", "border", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "reduce", "acc", "marginLeft", "transition", "className", "size", "borderRadius", "Body", "noValidate", "onSubmit", "md", "Group", "Label", "Control", "type", "onChange", "target", "placeholder", "required", "<PERSON><PERSON><PERSON>", "as", "rows", "onClick", "disabled", "_currentVariant$fabri", "_currentVariant$fabri2", "currentV<PERSON>t", "currentValue", "variantOptions", "_variant$fabric_defin3", "_variant$fabric_defin4", "isAlreadySelected", "isDisabled", "Header", "animation", "options", "components", "Option", "selectedOption", "styles", "control", "provided", "borderColor", "boxShadow", "valueContainer", "option", "state", "isSelected", "isFocused", "cursor", "step", "min", "isInvalid", "sizeIndex", "sizeKey", "toLowerCase", "sm", "val", "Math", "max", "bg", "role", "show", "onHide", "centered", "closeButton", "Title", "Footer", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/System Development Project/pri new/frontend/src/pages/AddCutting.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport Select from 'react-select';\r\nimport RoleBasedNavBar from \"../components/RoleBasedNavBar\";\r\nimport { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Spinner, <PERSON><PERSON>, Container, Badge, Modal } from 'react-bootstrap';\r\nimport { BsScissors, BsPlus, BsTrash, BsCheck2Circle, BsExclamationTriangle, BsFilePdf } from 'react-icons/bs';\r\nimport jsPDF from 'jspdf';\r\n\r\nconst AddCuttingRecord = () => {\r\n  // Overall cutting record fields\r\n  const [allFabricVariants, setAllFabricVariants] = useState([]);\r\n  const [cuttingDate, setCuttingDate] = useState('');\r\n  const [description, setDescription] = useState('');\r\n  const [productName, setProductName] = useState('');\r\n\r\n  // Cutting detail rows\r\n  const [details, setDetails] = useState([\r\n    { fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }\r\n  ]);\r\n\r\n  // Loading, error, success states\r\n  const [loadingVariants, setLoadingVariants] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isSidebarOpen, setIsSidebarOpen] = useState(window.innerWidth >= 768);\r\n  const [validated, setValidated] = useState(false);\r\n  const [showPdfModal, setShowPdfModal] = useState(false);\r\n  const [submittedRecord, setSubmittedRecord] = useState(null);\r\n\r\n  // Add resize event listener to update sidebar state\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsSidebarOpen(window.innerWidth >= 768);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  // Fetch all fabric variants on mount\r\n  useEffect(() => {\r\n    setLoadingVariants(true);\r\n    axios.get(\"http://localhost:8000/api/fabric-variants/\")\r\n      .then((res) => {\r\n        setAllFabricVariants(res.data);\r\n        setLoadingVariants(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error('Error fetching fabric variants:', err);\r\n        setError('Failed to load fabric variants. Please try again.');\r\n        setLoadingVariants(false);\r\n      });\r\n  }, []);\r\n\r\n  // Add a new empty detail row\r\n  const addDetailRow = () => {\r\n    setDetails([...details, { fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]);\r\n  };\r\n\r\n  // Delete a detail row\r\n  const removeDetailRow = (index) => {\r\n    const newDetails = details.filter((_, i) => i !== index);\r\n    setDetails(newDetails);\r\n  };\r\n\r\n  // Check if a fabric variant is already selected in another detail row\r\n  const isDuplicateFabricVariant = (variantId, currentIndex) => {\r\n    return details.some((detail, idx) =>\r\n      idx !== currentIndex && detail.fabric_variant === variantId && variantId !== ''\r\n    );\r\n  };\r\n\r\n  // Handle change for each detail row field\r\n  const handleDetailChange = (index, field, value) => {\r\n    const newDetails = [...details];\r\n\r\n    // If changing fabric variant, check for duplicates\r\n    if (field === 'fabric_variant') {\r\n      if (isDuplicateFabricVariant(value, index)) {\r\n        setError(`This fabric variant is already selected in another detail. Please select a different variant.`);\r\n        return; // Don't update the state if duplicate\r\n      } else {\r\n        setError(''); // Clear error if no duplicate\r\n      }\r\n    }\r\n\r\n    newDetails[index][field] = value;\r\n    setDetails(newDetails);\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Form validation\r\n    const form = e.currentTarget;\r\n    if (form.checkValidity() === false) {\r\n      e.stopPropagation();\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    // Check if any detail has a fabric variant selected\r\n    const hasValidDetails = details.some(detail => detail.fabric_variant);\r\n    if (!hasValidDetails) {\r\n      setError('Please select at least one fabric variant for your cutting details.');\r\n      return;\r\n    }\r\n\r\n    // Check for duplicate fabric variants\r\n    const selectedVariants = details.map(detail => detail.fabric_variant).filter(Boolean);\r\n    const uniqueVariants = [...new Set(selectedVariants)];\r\n\r\n    if (selectedVariants.length !== uniqueVariants.length) {\r\n      setError('You have selected the same fabric variant in multiple details. Please use unique fabric variants for each detail.');\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    // Validate yard availability for each detail\r\n    let yardValidationError = false;\r\n    details.forEach(detail => {\r\n      if (detail.fabric_variant) {\r\n        const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\r\n        if (variant && parseFloat(detail.yard_usage) > (variant.available_yard || variant.total_yard)) {\r\n          yardValidationError = true;\r\n          setError(`Yard usage for ${variant.color_name || variant.color} exceeds available yards (${variant.available_yard || variant.total_yard} yards available).`);\r\n        }\r\n      }\r\n    });\r\n\r\n    if (yardValidationError) {\r\n      setValidated(true);\r\n      return;\r\n    }\r\n\r\n    setValidated(true);\r\n    setIsSubmitting(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    const payload = {\r\n      cutting_date: cuttingDate,\r\n      description: description,\r\n      product_name: productName,\r\n      details: details\r\n    };\r\n\r\n    try {\r\n      const response = await axios.post(\"http://localhost:8000/api/cutting/cutting-records/\", payload);\r\n      setSuccess('Cutting record created successfully!');\r\n\r\n      // Store the submitted record for PDF generation\r\n      const fabricNames = new Set();\r\n      const recordData = {\r\n        ...response.data,\r\n        details: response.data.details.map(detail => {\r\n          const variant = allFabricVariants.find(v => v.id === detail.fabric_variant);\r\n          if (variant?.fabric_definition_data?.fabric_name) {\r\n            fabricNames.add(variant.fabric_definition_data.fabric_name);\r\n          }\r\n          return {\r\n            ...detail,\r\n            color: variant?.color || 'Unknown',\r\n            color_name: variant?.color_name || variant?.color || 'Unknown',\r\n            fabric_name: variant?.fabric_definition_data?.fabric_name || 'Unknown'\r\n          };\r\n        }),\r\n        fabric_names: Array.from(fabricNames).join(', ') || 'Unknown',\r\n        totalQuantities: totalQuantities\r\n      };\r\n\r\n      setSubmittedRecord(recordData);\r\n\r\n      // Show the PDF generation modal\r\n      setShowPdfModal(true);\r\n    } catch (err) {\r\n      console.error('Error creating cutting record:', err);\r\n      if (err.response && err.response.data) {\r\n        // Display more specific error message if available\r\n        const errorMessage = typeof err.response.data === 'string'\r\n          ? err.response.data\r\n          : 'Failed to create cutting record. Please check your inputs.';\r\n        setError(errorMessage);\r\n      } else {\r\n        setError('Failed to create cutting record. Please try again.');\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Function to generate PDF directly without using html2canvas\r\n  const generatePDF = () => {\r\n    if (!submittedRecord) return;\r\n\r\n    try {\r\n      // Create a new PDF document\r\n      const pdf = new jsPDF({\r\n        orientation: 'portrait',\r\n        unit: 'mm',\r\n        format: 'a4'\r\n      });\r\n\r\n      // Set font sizes and styles\r\n      const titleFontSize = 18;\r\n      const headingFontSize = 14;\r\n      const normalFontSize = 10;\r\n      const smallFontSize = 8;\r\n\r\n      // Add title\r\n      pdf.setFontSize(titleFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Cutting Record', 105, 20, { align: 'center' });\r\n\r\n      // Add general information section\r\n      pdf.setFontSize(headingFontSize);\r\n      pdf.text('General Information', 20, 35);\r\n\r\n      pdf.setFontSize(normalFontSize);\r\n      pdf.setFont('helvetica', 'normal');\r\n\r\n      // Draw table for general info\r\n      pdf.line(20, 40, 190, 40); // Top horizontal line\r\n\r\n      const generalInfoData = [\r\n        ['Record ID', submittedRecord.id.toString()],\r\n        ['Product Name', submittedRecord.product_name],\r\n        ['Fabrics Used', submittedRecord.fabric_names],\r\n        ['Cutting Date', new Date(submittedRecord.cutting_date).toLocaleDateString()],\r\n        ['Description', submittedRecord.description || 'N/A']\r\n      ];\r\n\r\n      let yPos = 45;\r\n      generalInfoData.forEach((row) => {\r\n        pdf.setFont('helvetica', 'bold');\r\n        pdf.text(row[0], 25, yPos);\r\n        pdf.setFont('helvetica', 'normal');\r\n        pdf.text(row[1], 80, yPos);\r\n        yPos += 8;\r\n        pdf.line(20, yPos - 3, 190, yPos - 3); // Horizontal line after each row\r\n      });\r\n\r\n      // Add fabric details section\r\n      pdf.setFontSize(headingFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Fabric Details', 20, yPos + 10);\r\n\r\n      // Table headers for fabric details\r\n      const headers = ['Fabric', 'Color', 'Yard Usage', 'XS', 'S', 'M', 'L', 'XL', 'Total'];\r\n      const colWidths = [35, 35, 20, 12, 12, 12, 12, 12, 15];\r\n\r\n      // Calculate starting positions for each column\r\n      const colPositions = [];\r\n      let currentPos = 20;\r\n      colWidths.forEach(width => {\r\n        colPositions.push(currentPos);\r\n        currentPos += width;\r\n      });\r\n\r\n      // Draw table header\r\n      yPos += 15;\r\n      pdf.setFontSize(normalFontSize);\r\n      pdf.setFont('helvetica', 'bold');\r\n\r\n      // Draw header background\r\n      pdf.setFillColor(240, 240, 240);\r\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\r\n\r\n      // Draw header text\r\n      headers.forEach((header, index) => {\r\n        pdf.text(header, colPositions[index] + 2, yPos);\r\n      });\r\n\r\n      // Draw horizontal line after header\r\n      yPos += 3;\r\n      pdf.line(20, yPos, 190, yPos);\r\n\r\n      // Draw table rows\r\n      pdf.setFont('helvetica', 'normal');\r\n      submittedRecord.details.forEach((detail) => {\r\n        yPos += 8;\r\n\r\n        // Calculate total for this row\r\n        const total = parseInt(detail.xs || 0) +\r\n                      parseInt(detail.s || 0) +\r\n                      parseInt(detail.m || 0) +\r\n                      parseInt(detail.l || 0) +\r\n                      parseInt(detail.xl || 0);\r\n\r\n        // Draw row data\r\n        pdf.text(detail.fabric_name || 'Unknown', colPositions[0] + 2, yPos);\r\n        pdf.text(detail.color_name || detail.color, colPositions[1] + 2, yPos);\r\n        pdf.text(`${detail.yard_usage} yards`, colPositions[2] + 2, yPos);\r\n        pdf.text(detail.xs?.toString() || '0', colPositions[3] + 2, yPos);\r\n        pdf.text(detail.s?.toString() || '0', colPositions[4] + 2, yPos);\r\n        pdf.text(detail.m?.toString() || '0', colPositions[5] + 2, yPos);\r\n        pdf.text(detail.l?.toString() || '0', colPositions[6] + 2, yPos);\r\n        pdf.text(detail.xl?.toString() || '0', colPositions[7] + 2, yPos);\r\n        pdf.text(total.toString(), colPositions[8] + 2, yPos);\r\n\r\n        // Draw horizontal line after row\r\n        yPos += 3;\r\n        pdf.line(20, yPos, 190, yPos);\r\n      });\r\n\r\n      // Draw totals row\r\n      yPos += 8;\r\n      pdf.setFillColor(240, 240, 240);\r\n      pdf.rect(20, yPos - 5, 170, 8, 'F');\r\n\r\n      pdf.setFont('helvetica', 'bold');\r\n      pdf.text('Total', colPositions[0] + 2, yPos);\r\n      pdf.text('', colPositions[1] + 2, yPos);\r\n      pdf.text(`${submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards`, colPositions[2] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.xs.toString(), colPositions[3] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.s.toString(), colPositions[4] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.m.toString(), colPositions[5] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.l.toString(), colPositions[6] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.xl.toString(), colPositions[7] + 2, yPos);\r\n      pdf.text(submittedRecord.totalQuantities.total.toString(), colPositions[8] + 2, yPos);\r\n\r\n      // Add footer\r\n      pdf.setFontSize(smallFontSize);\r\n      pdf.setFont('helvetica', 'italic');\r\n      pdf.text(`Generated on: ${new Date().toLocaleString()}`, 105, 280, { align: 'center' });\r\n      pdf.text('Fashion Garment Management System', 105, 285, { align: 'center' });\r\n\r\n      // Save the PDF\r\n      pdf.save(`Cutting_Record_${submittedRecord.id}_${submittedRecord.product_name}.pdf`);\r\n\r\n      // Reset form after PDF generation\r\n      setShowPdfModal(false);\r\n      setCuttingDate('');\r\n      setDescription('');\r\n      setProductName('');\r\n      setDetails([{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]);\r\n      setValidated(false);\r\n    } catch (error) {\r\n      console.error('Error generating PDF:', error);\r\n      setError('Failed to generate PDF. Please try again.');\r\n      setShowPdfModal(false);\r\n    }\r\n  };\r\n\r\n  // Function to handle modal close without generating PDF\r\n  const handleCloseModal = () => {\r\n    setShowPdfModal(false);\r\n    // Reset form\r\n    setCuttingDate('');\r\n    setDescription('');\r\n    setProductName('');\r\n    setDetails([{ fabric_variant: '', yard_usage: '', xs: 0, s: 0, m: 0, l: 0, xl: 0 }]);\r\n    setValidated(false);\r\n  };\r\n\r\n  // Custom option component that shows a color swatch + label\r\n  const ColourOption = ({ data, innerRef, innerProps }) => (\r\n    <div\r\n      ref={innerRef}\r\n      {...innerProps}\r\n      style={{ display: 'flex', alignItems: 'center', padding: '4px' }}\r\n    >\r\n      <div\r\n        style={{\r\n          width: 16,\r\n          height: 16,\r\n          backgroundColor: data.color,\r\n          marginRight: 8,\r\n          border: '1px solid #ccc'\r\n        }}\r\n      />\r\n      <span>{data.label}</span>\r\n    </div>\r\n  );\r\n\r\n  // Calculate total quantities for all details\r\n  const totalQuantities = details.reduce(\r\n    (acc, detail) => {\r\n      acc.xs += parseInt(detail.xs) || 0;\r\n      acc.s += parseInt(detail.s) || 0;\r\n      acc.m += parseInt(detail.m) || 0;\r\n      acc.l += parseInt(detail.l) || 0;\r\n      acc.xl += parseInt(detail.xl) || 0;\r\n      acc.total += (parseInt(detail.xs) || 0) +\r\n                  (parseInt(detail.s) || 0) +\r\n                  (parseInt(detail.m) || 0) +\r\n                  (parseInt(detail.l) || 0) +\r\n                  (parseInt(detail.xl) || 0);\r\n      acc.yard_usage += parseFloat(detail.yard_usage) || 0;\r\n      return acc;\r\n    },\r\n    { xs: 0, s: 0, m: 0, l: 0, xl: 0, total: 0, yard_usage: 0 }\r\n  );\r\n\r\n  return (\r\n    <>\r\n      <RoleBasedNavBar />\r\n      <div\r\n        style={{\r\n          marginLeft: isSidebarOpen ? \"240px\" : \"70px\",\r\n          width: `calc(100% - ${isSidebarOpen ? \"240px\" : \"70px\"})`,\r\n          transition: \"all 0.3s ease\",\r\n          padding: \"20px\"\r\n        }}\r\n      >\r\n        <h2 className=\"mb-4\">\r\n          <BsScissors className=\"me-2\" />\r\n          Add Cutting Record\r\n        </h2>\r\n\r\n        {success && (\r\n          <Alert variant=\"success\" className=\"d-flex align-items-center\">\r\n            <BsCheck2Circle className=\"me-2\" size={20} />\r\n            {success}\r\n          </Alert>\r\n        )}\r\n\r\n        {error && (\r\n          <Alert variant=\"danger\" className=\"d-flex align-items-center\">\r\n            <BsExclamationTriangle className=\"me-2\" size={20} />\r\n            {error}\r\n          </Alert>\r\n        )}\r\n\r\n        <Card className=\"mb-4 shadow-sm\" style={{ backgroundColor: \"#D9EDFB\", borderRadius: \"10px\" }}>\r\n          <Card.Body>\r\n            <Form noValidate validated={validated} onSubmit={handleSubmit}>\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Product Name</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      value={productName}\r\n                      onChange={(e) => setProductName(e.target.value)}\r\n                      placeholder=\"Enter product name\"\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please provide a product name.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Cutting Date</strong></Form.Label>\r\n                    <Form.Control\r\n                      type=\"date\"\r\n                      value={cuttingDate}\r\n                      onChange={(e) => setCuttingDate(e.target.value)}\r\n                      required\r\n                    />\r\n                    <Form.Control.Feedback type=\"invalid\">\r\n                      Please select a cutting date.\r\n                    </Form.Control.Feedback>\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label><strong>Description</strong></Form.Label>\r\n                    <Form.Control\r\n                      as=\"textarea\"\r\n                      rows={3}\r\n                      value={description}\r\n                      onChange={(e) => setDescription(e.target.value)}\r\n                      placeholder=\"Enter details about this cutting record...\"\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <div className=\"d-flex justify-content-between align-items-center mt-4 mb-3 border-bottom pb-2\">\r\n                <h4 className=\"mb-0\">Fabric Details</h4>\r\n                <Button\r\n                  variant=\"success\"\r\n                  size=\"sm\"\r\n                  onClick={addDetailRow}\r\n                  disabled={isSubmitting}\r\n                >\r\n                  <BsPlus className=\"me-1\" /> Add Fabric Variant\r\n                </Button>\r\n              </div>\r\n\r\n              {details.map((detail, index) => {\r\n                // Find the selected variant object to set the value in React-Select\r\n                const currentVariant = allFabricVariants.find(v => v.id === detail.fabric_variant);\r\n                const currentValue = currentVariant\r\n                  ? {\r\n                      value: currentVariant.id,\r\n                      label: `${currentVariant.fabric_definition_data?.fabric_name || 'Unknown'} - ${currentVariant.color_name || currentVariant.color} (${currentVariant.available_yard || currentVariant.total_yard} yards available)`,\r\n                      color: currentVariant.color,\r\n                      available_yard: currentVariant.available_yard || currentVariant.total_yard,\r\n                      total_yard: currentVariant.total_yard,\r\n                      fabric_name: currentVariant.fabric_definition_data?.fabric_name || 'Unknown'\r\n                    }\r\n                  : null;\r\n\r\n                // Prepare the variant options for React-Select\r\n                const variantOptions = allFabricVariants.map((variant) => {\r\n                  // Check if this variant is already selected in another detail\r\n                  const isAlreadySelected = isDuplicateFabricVariant(variant.id, index);\r\n\r\n                  return {\r\n                    value: variant.id,\r\n                    label: `${variant.fabric_definition_data?.fabric_name || 'Unknown'} - ${variant.color_name || variant.color} (${variant.available_yard || variant.total_yard} yards available)${isAlreadySelected ? ' - Already Selected' : ''}`,\r\n                    color: variant.color,\r\n                    available_yard: variant.available_yard || variant.total_yard,\r\n                    total_yard: variant.total_yard,\r\n                    fabric_name: variant.fabric_definition_data?.fabric_name || 'Unknown',\r\n                    isDisabled: isAlreadySelected // Disable options that are already selected\r\n                  };\r\n                });\r\n\r\n                return (\r\n                  <Card key={index} className=\"mb-3 border\">\r\n                    <Card.Header className=\"d-flex justify-content-between align-items-center bg-light\">\r\n                      <h5 className=\"mb-0\">Detail #{index + 1}</h5>\r\n                      <Button\r\n                        variant=\"outline-danger\"\r\n                        size=\"sm\"\r\n                        onClick={() => removeDetailRow(index)}\r\n                        disabled={details.length === 1}\r\n                      >\r\n                        <BsTrash className=\"me-1\" /> Remove\r\n                      </Button>\r\n                    </Card.Header>\r\n                    <Card.Body>\r\n                      <Row>\r\n                        <Col md={6}>\r\n                          <Form.Group className=\"mb-3\">\r\n                            <Form.Label><strong>Fabric Variant (Color)</strong></Form.Label>\r\n                            {loadingVariants ? (\r\n                              <div className=\"d-flex align-items-center\">\r\n                                <Spinner animation=\"border\" size=\"sm\" className=\"me-2\" />\r\n                                <span>Loading variants...</span>\r\n                              </div>\r\n                            ) : (\r\n                              <>\r\n                                <Select\r\n                                  options={variantOptions}\r\n                                  components={{ Option: ColourOption }}\r\n                                  value={currentValue}\r\n                                  onChange={(selectedOption) => {\r\n                                    handleDetailChange(index, 'fabric_variant', selectedOption.value);\r\n                                  }}\r\n                                  placeholder=\"Select Fabric Variant\"\r\n                                  styles={{\r\n                                    control: (provided) => ({\r\n                                      ...provided,\r\n                                      borderColor: '#ddd',\r\n                                      boxShadow: 'none',\r\n                                      height: '38px',\r\n                                      '&:hover': {\r\n                                        borderColor: '#aaa'\r\n                                      }\r\n                                    }),\r\n                                    valueContainer: (provided) => ({\r\n                                      ...provided,\r\n                                      height: '38px',\r\n                                      padding: '0 8px'\r\n                                    }),\r\n                                    option: (provided, state) => ({\r\n                                      ...provided,\r\n                                      backgroundColor: state.isDisabled\r\n                                        ? '#f8f9fa'\r\n                                        : state.isSelected\r\n                                          ? '#007bff'\r\n                                          : state.isFocused\r\n                                            ? '#e9ecef'\r\n                                            : 'white',\r\n                                      color: state.isDisabled\r\n                                        ? '#6c757d'\r\n                                        : state.isSelected\r\n                                          ? 'white'\r\n                                          : 'black',\r\n                                      cursor: state.isDisabled ? 'not-allowed' : 'default'\r\n                                    })\r\n                                  }}\r\n                                />\r\n                                {!detail.fabric_variant && validated && (\r\n                                  <div className=\"text-danger small mt-1\">\r\n                                    Please select a fabric variant.\r\n                                  </div>\r\n                                )}\r\n                              </>\r\n                            )}\r\n                          </Form.Group>\r\n                        </Col>\r\n                        <Col md={6}>\r\n                          <Form.Group className=\"mb-3\">\r\n                            <div className=\"d-flex justify-content-between align-items-center mb-1\">\r\n                              <Form.Label className=\"mb-0\"><strong>Yard Usage</strong></Form.Label>\r\n                              {currentVariant && (\r\n                                <span className={parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"text-danger small\" : \"text-success small\"}>\r\n                                  Available: {currentVariant.available_yard || currentVariant.total_yard} yards\r\n                                </span>\r\n                              )}\r\n                            </div>\r\n                            <Form.Control\r\n                              type=\"number\"\r\n                              step=\"0.01\"\r\n                              min=\"0\"\r\n                              value={detail.yard_usage}\r\n                              onChange={(e) => handleDetailChange(index, 'yard_usage', e.target.value)}\r\n                              required\r\n                              placeholder=\"Enter yards used\"\r\n                              isInvalid={currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard)}\r\n                              className={currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard) ? \"border-danger\" : \"\"}\r\n                              style={{ height: '38px' }}\r\n                            />\r\n                            <Form.Control.Feedback type=\"invalid\">\r\n                              {currentVariant && parseFloat(detail.yard_usage) > (currentVariant.available_yard || currentVariant.total_yard)\r\n                                ? `Exceeds available yards (${currentVariant.available_yard || currentVariant.total_yard} yards available)`\r\n                                : \"Please enter valid yard usage.\"}\r\n                            </Form.Control.Feedback>\r\n                          </Form.Group>\r\n                        </Col>\r\n                      </Row>\r\n\r\n                      <Form.Label className=\"mt-2\"><strong>Size Quantities</strong></Form.Label>\r\n                      <Row>\r\n                        {[\"XS\", \"S\", \"M\", \"L\", \"XL\"].map((size, sizeIndex) => {\r\n                          const sizeKey = size.toLowerCase();\r\n                          return (\r\n                            <Col key={sizeIndex} xs={6} sm={4} md={2} className=\"mb-3\">\r\n                              <Form.Group>\r\n                                <Form.Label className=\"text-center d-block\">{size}</Form.Label>\r\n                                <Form.Control\r\n                                  type=\"number\"\r\n                                  min=\"0\"\r\n                                  value={detail[sizeKey]}\r\n                                  onChange={(e) => {\r\n                                    const val = Math.max(0, parseInt(e.target.value || 0));\r\n                                    handleDetailChange(index, sizeKey, val);\r\n                                  }}\r\n                                  className=\"text-center\"\r\n                                />\r\n                              </Form.Group>\r\n                            </Col>\r\n                          );\r\n                        })}\r\n                        <Col xs={6} sm={4} md={2} className=\"mb-3\">\r\n                          <Form.Group>\r\n                            <Form.Label className=\"text-center d-block\">Total</Form.Label>\r\n                            <div className=\"form-control text-center bg-light\">\r\n                              {parseInt(detail.xs || 0) +\r\n                               parseInt(detail.s || 0) +\r\n                               parseInt(detail.m || 0) +\r\n                               parseInt(detail.l || 0) +\r\n                               parseInt(detail.xl || 0)}\r\n                            </div>\r\n                          </Form.Group>\r\n                        </Col>\r\n                      </Row>\r\n                    </Card.Body>\r\n                  </Card>\r\n                );\r\n              })}\r\n\r\n              <div className=\"d-flex justify-content-end mb-4\">\r\n                <Card className=\"border-0\" style={{ backgroundColor: \"#e8f4fe\" }}>\r\n                  <Card.Body className=\"py-2\">\r\n                    <div className=\"d-flex flex-column\">\r\n                      <div className=\"d-flex align-items-center mb-2\">\r\n                        <strong className=\"me-2\">Total Quantities:</strong>\r\n                        <Badge bg=\"primary\" className=\"me-1\">XS: {totalQuantities.xs}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">S: {totalQuantities.s}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">M: {totalQuantities.m}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">L: {totalQuantities.l}</Badge>\r\n                        <Badge bg=\"primary\" className=\"me-1\">XL: {totalQuantities.xl}</Badge>\r\n                        <Badge bg=\"success\" className=\"ms-2\">Total: {totalQuantities.total}</Badge>\r\n                      </div>\r\n                      <div className=\"d-flex align-items-center\">\r\n                        <strong className=\"me-2\">Total Yard Usage:</strong>\r\n                        <Badge bg=\"info\">{totalQuantities.yard_usage.toFixed(2)} yards</Badge>\r\n                      </div>\r\n                    </div>\r\n                  </Card.Body>\r\n                </Card>\r\n              </div>\r\n\r\n              <div className=\"d-flex justify-content-center mt-4\">\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"primary\"\r\n                  size=\"lg\"\r\n                  disabled={isSubmitting}\r\n                  className=\"px-5\"\r\n                >\r\n                  {isSubmitting ? (\r\n                    <>\r\n                      <Spinner as=\"span\" animation=\"border\" size=\"sm\" role=\"status\" aria-hidden=\"true\" className=\"me-2\" />\r\n                      Submitting...\r\n                    </>\r\n                  ) : (\r\n                    'Submit Cutting Record'\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </Form>\r\n          </Card.Body>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* PDF Generation Modal */}\r\n      <Modal show={showPdfModal} onHide={handleCloseModal} size=\"lg\" centered>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>Generate Cutting Record PDF</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n          <p>Would you like to generate a PDF for this cutting record?</p>\r\n\r\n          {submittedRecord && (\r\n            <div className=\"mb-3\">\r\n              <p>The PDF will include the following information:</p>\r\n              <ul>\r\n                <li><strong>Product Name:</strong> {submittedRecord.product_name}</li>\r\n                <li><strong>Fabric:</strong> {submittedRecord.fabric_name}</li>\r\n                <li><strong>Cutting Date:</strong> {new Date(submittedRecord.cutting_date).toLocaleDateString()}</li>\r\n                <li><strong>Total Quantities:</strong> XS: {submittedRecord.totalQuantities.xs},\r\n                  S: {submittedRecord.totalQuantities.s},\r\n                  M: {submittedRecord.totalQuantities.m},\r\n                  L: {submittedRecord.totalQuantities.l},\r\n                  XL: {submittedRecord.totalQuantities.xl}</li>\r\n                <li><strong>Total Items:</strong> {submittedRecord.totalQuantities.total}</li>\r\n                <li><strong>Total Yard Usage:</strong> {submittedRecord.totalQuantities.yard_usage.toFixed(2)} yards</li>\r\n              </ul>\r\n            </div>\r\n          )}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={handleCloseModal}>\r\n            No, Skip\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={generatePDF}>\r\n            <BsFilePdf className=\"me-2\" /> Generate PDF\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AddCuttingRecord;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AACvG,SAASC,UAAU,EAAEC,MAAM,EAAEC,OAAO,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,SAAS,QAAQ,gBAAgB;AAC9G,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,CACrC;IAAEsC,cAAc,EAAE,EAAE;IAAEC,UAAU,EAAE,EAAE;IAAEC,EAAE,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,EAAE,EAAE;EAAE,CAAC,CACvE,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAACuD,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAC5E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6D,eAAe,EAAEC,kBAAkB,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,MAAM8D,YAAY,GAAGA,CAAA,KAAM;MACzBT,gBAAgB,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IAC5C,CAAC;IAEDD,MAAM,CAACS,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMR,MAAM,CAACU,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9D,SAAS,CAAC,MAAM;IACd6C,kBAAkB,CAAC,IAAI,CAAC;IACxB5C,KAAK,CAACgE,GAAG,CAAC,4CAA4C,CAAC,CACpDC,IAAI,CAAEC,GAAG,IAAK;MACbvC,oBAAoB,CAACuC,GAAG,CAACC,IAAI,CAAC;MAC9BvB,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC,CACDwB,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACzB,KAAK,CAAC,iCAAiC,EAAEwB,GAAG,CAAC;MACrDvB,QAAQ,CAAC,mDAAmD,CAAC;MAC7DF,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2B,YAAY,GAAGA,CAAA,KAAM;IACzBpC,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAE;MAAEE,cAAc,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAC,CAAC,CAAC;EAClG,CAAC;;EAED;EACA,MAAM8B,eAAe,GAAIC,KAAK,IAAK;IACjC,MAAMC,UAAU,GAAGxC,OAAO,CAACyC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IACxDtC,UAAU,CAACuC,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMI,wBAAwB,GAAGA,CAACC,SAAS,EAAEC,YAAY,KAAK;IAC5D,OAAO9C,OAAO,CAAC+C,IAAI,CAAC,CAACC,MAAM,EAAEC,GAAG,KAC9BA,GAAG,KAAKH,YAAY,IAAIE,MAAM,CAAC9C,cAAc,KAAK2C,SAAS,IAAIA,SAAS,KAAK,EAC/E,CAAC;EACH,CAAC;;EAED;EACA,MAAMK,kBAAkB,GAAGA,CAACX,KAAK,EAAEY,KAAK,EAAEC,KAAK,KAAK;IAClD,MAAMZ,UAAU,GAAG,CAAC,GAAGxC,OAAO,CAAC;;IAE/B;IACA,IAAImD,KAAK,KAAK,gBAAgB,EAAE;MAC9B,IAAIP,wBAAwB,CAACQ,KAAK,EAAEb,KAAK,CAAC,EAAE;QAC1C3B,QAAQ,CAAC,+FAA+F,CAAC;QACzG,OAAO,CAAC;MACV,CAAC,MAAM;QACLA,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB;IACF;IAEA4B,UAAU,CAACD,KAAK,CAAC,CAACY,KAAK,CAAC,GAAGC,KAAK;IAChCnD,UAAU,CAACuC,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMa,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,IAAI,GAAGF,CAAC,CAACG,aAAa;IAC5B,IAAID,IAAI,CAACE,aAAa,CAAC,CAAC,KAAK,KAAK,EAAE;MAClCJ,CAAC,CAACK,eAAe,CAAC,CAAC;MACnBrC,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;;IAEA;IACA,MAAMsC,eAAe,GAAG5D,OAAO,CAAC+C,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAC9C,cAAc,CAAC;IACrE,IAAI,CAAC0D,eAAe,EAAE;MACpBhD,QAAQ,CAAC,qEAAqE,CAAC;MAC/E;IACF;;IAEA;IACA,MAAMiD,gBAAgB,GAAG7D,OAAO,CAAC8D,GAAG,CAACd,MAAM,IAAIA,MAAM,CAAC9C,cAAc,CAAC,CAACuC,MAAM,CAACsB,OAAO,CAAC;IACrF,MAAMC,cAAc,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACJ,gBAAgB,CAAC,CAAC;IAErD,IAAIA,gBAAgB,CAACK,MAAM,KAAKF,cAAc,CAACE,MAAM,EAAE;MACrDtD,QAAQ,CAAC,mHAAmH,CAAC;MAC7HU,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;;IAEA;IACA,IAAI6C,mBAAmB,GAAG,KAAK;IAC/BnE,OAAO,CAACoE,OAAO,CAACpB,MAAM,IAAI;MACxB,IAAIA,MAAM,CAAC9C,cAAc,EAAE;QACzB,MAAMmE,OAAO,GAAG7E,iBAAiB,CAAC8E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKxB,MAAM,CAAC9C,cAAc,CAAC;QAC3E,IAAImE,OAAO,IAAII,UAAU,CAACzB,MAAM,CAAC7C,UAAU,CAAC,IAAIkE,OAAO,CAACK,cAAc,IAAIL,OAAO,CAACM,UAAU,CAAC,EAAE;UAC7FR,mBAAmB,GAAG,IAAI;UAC1BvD,QAAQ,CAAC,kBAAkByD,OAAO,CAACO,UAAU,IAAIP,OAAO,CAACQ,KAAK,6BAA6BR,OAAO,CAACK,cAAc,IAAIL,OAAO,CAACM,UAAU,oBAAoB,CAAC;QAC9J;MACF;IACF,CAAC,CAAC;IAEF,IAAIR,mBAAmB,EAAE;MACvB7C,YAAY,CAAC,IAAI,CAAC;MAClB;IACF;IAEAA,YAAY,CAAC,IAAI,CAAC;IAClBN,eAAe,CAAC,IAAI,CAAC;IACrBJ,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,MAAMgE,OAAO,GAAG;MACdC,YAAY,EAAErF,WAAW;MACzBE,WAAW,EAAEA,WAAW;MACxBoF,YAAY,EAAElF,WAAW;MACzBE,OAAO,EAAEA;IACX,CAAC;IAED,IAAI;MACF,MAAMiF,QAAQ,GAAG,MAAMnH,KAAK,CAACoH,IAAI,CAAC,oDAAoD,EAAEJ,OAAO,CAAC;MAChGhE,UAAU,CAAC,sCAAsC,CAAC;;MAElD;MACA,MAAMqE,WAAW,GAAG,IAAIlB,GAAG,CAAC,CAAC;MAC7B,MAAMmB,UAAU,GAAG;QACjB,GAAGH,QAAQ,CAAChD,IAAI;QAChBjC,OAAO,EAAEiF,QAAQ,CAAChD,IAAI,CAACjC,OAAO,CAAC8D,GAAG,CAACd,MAAM,IAAI;UAAA,IAAAqC,qBAAA,EAAAC,sBAAA;UAC3C,MAAMjB,OAAO,GAAG7E,iBAAiB,CAAC8E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKxB,MAAM,CAAC9C,cAAc,CAAC;UAC3E,IAAImE,OAAO,aAAPA,OAAO,gBAAAgB,qBAAA,GAAPhB,OAAO,CAAEkB,sBAAsB,cAAAF,qBAAA,eAA/BA,qBAAA,CAAiCG,WAAW,EAAE;YAChDL,WAAW,CAACM,GAAG,CAACpB,OAAO,CAACkB,sBAAsB,CAACC,WAAW,CAAC;UAC7D;UACA,OAAO;YACL,GAAGxC,MAAM;YACT6B,KAAK,EAAE,CAAAR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,KAAK,KAAI,SAAS;YAClCD,UAAU,EAAE,CAAAP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEO,UAAU,MAAIP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEQ,KAAK,KAAI,SAAS;YAC9DW,WAAW,EAAE,CAAAnB,OAAO,aAAPA,OAAO,wBAAAiB,sBAAA,GAAPjB,OAAO,CAAEkB,sBAAsB,cAAAD,sBAAA,uBAA/BA,sBAAA,CAAiCE,WAAW,KAAI;UAC/D,CAAC;QACH,CAAC,CAAC;QACFE,YAAY,EAAEC,KAAK,CAACC,IAAI,CAACT,WAAW,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC,IAAI,SAAS;QAC7DC,eAAe,EAAEA;MACnB,CAAC;MAEDpE,kBAAkB,CAAC0D,UAAU,CAAC;;MAE9B;MACA5D,eAAe,CAAC,IAAI,CAAC;IACvB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZC,OAAO,CAACzB,KAAK,CAAC,gCAAgC,EAAEwB,GAAG,CAAC;MACpD,IAAIA,GAAG,CAAC8C,QAAQ,IAAI9C,GAAG,CAAC8C,QAAQ,CAAChD,IAAI,EAAE;QACrC;QACA,MAAM8D,YAAY,GAAG,OAAO5D,GAAG,CAAC8C,QAAQ,CAAChD,IAAI,KAAK,QAAQ,GACtDE,GAAG,CAAC8C,QAAQ,CAAChD,IAAI,GACjB,4DAA4D;QAChErB,QAAQ,CAACmF,YAAY,CAAC;MACxB,CAAC,MAAM;QACLnF,QAAQ,CAAC,oDAAoD,CAAC;MAChE;IACF,CAAC,SAAS;MACRI,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAMgF,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACvE,eAAe,EAAE;IAEtB,IAAI;MACF;MACA,MAAMwE,GAAG,GAAG,IAAIhH,KAAK,CAAC;QACpBiH,WAAW,EAAE,UAAU;QACvBC,IAAI,EAAE,IAAI;QACVC,MAAM,EAAE;MACV,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG,EAAE;MACxB,MAAMC,eAAe,GAAG,EAAE;MAC1B,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,aAAa,GAAG,CAAC;;MAEvB;MACAP,GAAG,CAACQ,WAAW,CAACJ,aAAa,CAAC;MAC9BJ,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAE,GAAG,EAAE,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAExD;MACAX,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACU,IAAI,CAAC,qBAAqB,EAAE,EAAE,EAAE,EAAE,CAAC;MAEvCV,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;;MAElC;MACAT,GAAG,CAACY,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;;MAE3B,MAAMC,eAAe,GAAG,CACtB,CAAC,WAAW,EAAErF,eAAe,CAAC+C,EAAE,CAACuC,QAAQ,CAAC,CAAC,CAAC,EAC5C,CAAC,cAAc,EAAEtF,eAAe,CAACuD,YAAY,CAAC,EAC9C,CAAC,cAAc,EAAEvD,eAAe,CAACiE,YAAY,CAAC,EAC9C,CAAC,cAAc,EAAE,IAAIsB,IAAI,CAACvF,eAAe,CAACsD,YAAY,CAAC,CAACkC,kBAAkB,CAAC,CAAC,CAAC,EAC7E,CAAC,aAAa,EAAExF,eAAe,CAAC7B,WAAW,IAAI,KAAK,CAAC,CACtD;MAED,IAAIsH,IAAI,GAAG,EAAE;MACbJ,eAAe,CAAC1C,OAAO,CAAE+C,GAAG,IAAK;QAC/BlB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;QAChCT,GAAG,CAACU,IAAI,CAACQ,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAED,IAAI,CAAC;QAC1BjB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClCT,GAAG,CAACU,IAAI,CAACQ,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAED,IAAI,CAAC;QAC1BA,IAAI,IAAI,CAAC;QACTjB,GAAG,CAACY,IAAI,CAAC,EAAE,EAAEK,IAAI,GAAG,CAAC,EAAE,GAAG,EAAEA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC;;MAEF;MACAjB,GAAG,CAACQ,WAAW,CAACH,eAAe,CAAC;MAChCL,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAEO,IAAI,GAAG,EAAE,CAAC;;MAEzC;MACA,MAAME,OAAO,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC;MACrF,MAAMC,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;MAEtD;MACA,MAAMC,YAAY,GAAG,EAAE;MACvB,IAAIC,UAAU,GAAG,EAAE;MACnBF,SAAS,CAACjD,OAAO,CAACoD,KAAK,IAAI;QACzBF,YAAY,CAACG,IAAI,CAACF,UAAU,CAAC;QAC7BA,UAAU,IAAIC,KAAK;MACrB,CAAC,CAAC;;MAEF;MACAN,IAAI,IAAI,EAAE;MACVjB,GAAG,CAACQ,WAAW,CAACF,cAAc,CAAC;MAC/BN,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;;MAEhC;MACAT,GAAG,CAACyB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BzB,GAAG,CAAC0B,IAAI,CAAC,EAAE,EAAET,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;;MAEnC;MACAE,OAAO,CAAChD,OAAO,CAAC,CAACwD,MAAM,EAAErF,KAAK,KAAK;QACjC0D,GAAG,CAACU,IAAI,CAACiB,MAAM,EAAEN,YAAY,CAAC/E,KAAK,CAAC,GAAG,CAAC,EAAE2E,IAAI,CAAC;MACjD,CAAC,CAAC;;MAEF;MACAA,IAAI,IAAI,CAAC;MACTjB,GAAG,CAACY,IAAI,CAAC,EAAE,EAAEK,IAAI,EAAE,GAAG,EAAEA,IAAI,CAAC;;MAE7B;MACAjB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCjF,eAAe,CAACzB,OAAO,CAACoE,OAAO,CAAEpB,MAAM,IAAK;QAAA,IAAA6E,UAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,SAAA,EAAAC,UAAA;QAC1Cf,IAAI,IAAI,CAAC;;QAET;QACA,MAAMgB,KAAK,GAAGC,QAAQ,CAACnF,MAAM,CAAC5C,EAAE,IAAI,CAAC,CAAC,GACxB+H,QAAQ,CAACnF,MAAM,CAAC3C,CAAC,IAAI,CAAC,CAAC,GACvB8H,QAAQ,CAACnF,MAAM,CAAC1C,CAAC,IAAI,CAAC,CAAC,GACvB6H,QAAQ,CAACnF,MAAM,CAACzC,CAAC,IAAI,CAAC,CAAC,GACvB4H,QAAQ,CAACnF,MAAM,CAACxC,EAAE,IAAI,CAAC,CAAC;;QAEtC;QACAyF,GAAG,CAACU,IAAI,CAAC3D,MAAM,CAACwC,WAAW,IAAI,SAAS,EAAE8B,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACpEjB,GAAG,CAACU,IAAI,CAAC3D,MAAM,CAAC4B,UAAU,IAAI5B,MAAM,CAAC6B,KAAK,EAAEyC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACtEjB,GAAG,CAACU,IAAI,CAAC,GAAG3D,MAAM,CAAC7C,UAAU,QAAQ,EAAEmH,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACjEjB,GAAG,CAACU,IAAI,CAAC,EAAAkB,UAAA,GAAA7E,MAAM,CAAC5C,EAAE,cAAAyH,UAAA,uBAATA,UAAA,CAAWd,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACjEjB,GAAG,CAACU,IAAI,CAAC,EAAAmB,SAAA,GAAA9E,MAAM,CAAC3C,CAAC,cAAAyH,SAAA,uBAARA,SAAA,CAAUf,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QAChEjB,GAAG,CAACU,IAAI,CAAC,EAAAoB,SAAA,GAAA/E,MAAM,CAAC1C,CAAC,cAAAyH,SAAA,uBAARA,SAAA,CAAUhB,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QAChEjB,GAAG,CAACU,IAAI,CAAC,EAAAqB,SAAA,GAAAhF,MAAM,CAACzC,CAAC,cAAAyH,SAAA,uBAARA,SAAA,CAAUjB,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QAChEjB,GAAG,CAACU,IAAI,CAAC,EAAAsB,UAAA,GAAAjF,MAAM,CAACxC,EAAE,cAAAyH,UAAA,uBAATA,UAAA,CAAWlB,QAAQ,CAAC,CAAC,KAAI,GAAG,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;QACjEjB,GAAG,CAACU,IAAI,CAACuB,KAAK,CAACnB,QAAQ,CAAC,CAAC,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;;QAErD;QACAA,IAAI,IAAI,CAAC;QACTjB,GAAG,CAACY,IAAI,CAAC,EAAE,EAAEK,IAAI,EAAE,GAAG,EAAEA,IAAI,CAAC;MAC/B,CAAC,CAAC;;MAEF;MACAA,IAAI,IAAI,CAAC;MACTjB,GAAG,CAACyB,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/BzB,GAAG,CAAC0B,IAAI,CAAC,EAAE,EAAET,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;MAEnCjB,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCT,GAAG,CAACU,IAAI,CAAC,OAAO,EAAEW,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MAC5CjB,GAAG,CAACU,IAAI,CAAC,EAAE,EAAEW,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACvCjB,GAAG,CAACU,IAAI,CAAC,GAAGlF,eAAe,CAACqE,eAAe,CAAC3F,UAAU,CAACiI,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAEd,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACrGjB,GAAG,CAACU,IAAI,CAAClF,eAAe,CAACqE,eAAe,CAAC1F,EAAE,CAAC2G,QAAQ,CAAC,CAAC,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MAClFjB,GAAG,CAACU,IAAI,CAAClF,eAAe,CAACqE,eAAe,CAACzF,CAAC,CAAC0G,QAAQ,CAAC,CAAC,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACjFjB,GAAG,CAACU,IAAI,CAAClF,eAAe,CAACqE,eAAe,CAACxF,CAAC,CAACyG,QAAQ,CAAC,CAAC,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACjFjB,GAAG,CAACU,IAAI,CAAClF,eAAe,CAACqE,eAAe,CAACvF,CAAC,CAACwG,QAAQ,CAAC,CAAC,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MACjFjB,GAAG,CAACU,IAAI,CAAClF,eAAe,CAACqE,eAAe,CAACtF,EAAE,CAACuG,QAAQ,CAAC,CAAC,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;MAClFjB,GAAG,CAACU,IAAI,CAAClF,eAAe,CAACqE,eAAe,CAACoC,KAAK,CAACnB,QAAQ,CAAC,CAAC,EAAEO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,IAAI,CAAC;;MAErF;MACAjB,GAAG,CAACQ,WAAW,CAACD,aAAa,CAAC;MAC9BP,GAAG,CAACS,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCT,GAAG,CAACU,IAAI,CAAC,iBAAiB,IAAIK,IAAI,CAAC,CAAC,CAACqB,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE;QAAEzB,KAAK,EAAE;MAAS,CAAC,CAAC;MACvFX,GAAG,CAACU,IAAI,CAAC,mCAAmC,EAAE,GAAG,EAAE,GAAG,EAAE;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;;MAE5E;MACAX,GAAG,CAACqC,IAAI,CAAC,kBAAkB7G,eAAe,CAAC+C,EAAE,IAAI/C,eAAe,CAACuD,YAAY,MAAM,CAAC;;MAEpF;MACAxD,eAAe,CAAC,KAAK,CAAC;MACtB7B,cAAc,CAAC,EAAE,CAAC;MAClBE,cAAc,CAAC,EAAE,CAAC;MAClBE,cAAc,CAAC,EAAE,CAAC;MAClBE,UAAU,CAAC,CAAC;QAAEC,cAAc,EAAE,EAAE;QAAEC,UAAU,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC,CAAC,CAAC;MACpFc,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CC,QAAQ,CAAC,2CAA2C,CAAC;MACrDY,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACA,MAAM+G,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/G,eAAe,CAAC,KAAK,CAAC;IACtB;IACA7B,cAAc,CAAC,EAAE,CAAC;IAClBE,cAAc,CAAC,EAAE,CAAC;IAClBE,cAAc,CAAC,EAAE,CAAC;IAClBE,UAAU,CAAC,CAAC;MAAEC,cAAc,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAC,CAAC,CAAC;IACpFc,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMkH,YAAY,GAAGA,CAAC;IAAEvG,IAAI;IAAEwG,QAAQ;IAAEC;EAAW,CAAC,kBAClDvJ,OAAA;IACEwJ,GAAG,EAAEF,QAAS;IAAA,GACVC,UAAU;IACdE,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAM,CAAE;IAAAC,QAAA,gBAEjE7J,OAAA;MACEyJ,KAAK,EAAE;QACLpB,KAAK,EAAE,EAAE;QACTyB,MAAM,EAAE,EAAE;QACVC,eAAe,EAAEjH,IAAI,CAAC4C,KAAK;QAC3BsE,WAAW,EAAE,CAAC;QACdC,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACFrK,OAAA;MAAA6J,QAAA,EAAO/G,IAAI,CAACwH;IAAK;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CACN;;EAED;EACA,MAAM1D,eAAe,GAAG9F,OAAO,CAAC0J,MAAM,CACpC,CAACC,GAAG,EAAE3G,MAAM,KAAK;IACf2G,GAAG,CAACvJ,EAAE,IAAI+H,QAAQ,CAACnF,MAAM,CAAC5C,EAAE,CAAC,IAAI,CAAC;IAClCuJ,GAAG,CAACtJ,CAAC,IAAI8H,QAAQ,CAACnF,MAAM,CAAC3C,CAAC,CAAC,IAAI,CAAC;IAChCsJ,GAAG,CAACrJ,CAAC,IAAI6H,QAAQ,CAACnF,MAAM,CAAC1C,CAAC,CAAC,IAAI,CAAC;IAChCqJ,GAAG,CAACpJ,CAAC,IAAI4H,QAAQ,CAACnF,MAAM,CAACzC,CAAC,CAAC,IAAI,CAAC;IAChCoJ,GAAG,CAACnJ,EAAE,IAAI2H,QAAQ,CAACnF,MAAM,CAACxC,EAAE,CAAC,IAAI,CAAC;IAClCmJ,GAAG,CAACzB,KAAK,IAAI,CAACC,QAAQ,CAACnF,MAAM,CAAC5C,EAAE,CAAC,IAAI,CAAC,KACzB+H,QAAQ,CAACnF,MAAM,CAAC3C,CAAC,CAAC,IAAI,CAAC,CAAC,IACxB8H,QAAQ,CAACnF,MAAM,CAAC1C,CAAC,CAAC,IAAI,CAAC,CAAC,IACxB6H,QAAQ,CAACnF,MAAM,CAACzC,CAAC,CAAC,IAAI,CAAC,CAAC,IACxB4H,QAAQ,CAACnF,MAAM,CAACxC,EAAE,CAAC,IAAI,CAAC,CAAC;IACtCmJ,GAAG,CAACxJ,UAAU,IAAIsE,UAAU,CAACzB,MAAM,CAAC7C,UAAU,CAAC,IAAI,CAAC;IACpD,OAAOwJ,GAAG;EACZ,CAAC,EACD;IAAEvJ,EAAE,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,EAAE,EAAE,CAAC;IAAE0H,KAAK,EAAE,CAAC;IAAE/H,UAAU,EAAE;EAAE,CAC5D,CAAC;EAED,oBACEhB,OAAA,CAAAE,SAAA;IAAA2J,QAAA,gBACE7J,OAAA,CAACnB,eAAe;MAAAqL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBrK,OAAA;MACEyJ,KAAK,EAAE;QACLgB,UAAU,EAAE3I,aAAa,GAAG,OAAO,GAAG,MAAM;QAC5CuG,KAAK,EAAE,eAAevG,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG;QACzD4I,UAAU,EAAE,eAAe;QAC3Bd,OAAO,EAAE;MACX,CAAE;MAAAC,QAAA,gBAEF7J,OAAA;QAAI2K,SAAS,EAAC,MAAM;QAAAd,QAAA,gBAClB7J,OAAA,CAACR,UAAU;UAACmL,SAAS,EAAC;QAAM;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,sBAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAEJ3I,OAAO,iBACN1B,OAAA,CAACZ,KAAK;QAAC8F,OAAO,EAAC,SAAS;QAACyF,SAAS,EAAC,2BAA2B;QAAAd,QAAA,gBAC5D7J,OAAA,CAACL,cAAc;UAACgL,SAAS,EAAC,MAAM;UAACC,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAC5C3I,OAAO;MAAA;QAAAwI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,EAEA7I,KAAK,iBACJxB,OAAA,CAACZ,KAAK;QAAC8F,OAAO,EAAC,QAAQ;QAACyF,SAAS,EAAC,2BAA2B;QAAAd,QAAA,gBAC3D7J,OAAA,CAACJ,qBAAqB;UAAC+K,SAAS,EAAC,MAAM;UAACC,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACnD7I,KAAK;MAAA;QAAA0I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDrK,OAAA,CAAClB,IAAI;QAAC6L,SAAS,EAAC,gBAAgB;QAAClB,KAAK,EAAE;UAAEM,eAAe,EAAE,SAAS;UAAEc,YAAY,EAAE;QAAO,CAAE;QAAAhB,QAAA,eAC3F7J,OAAA,CAAClB,IAAI,CAACgM,IAAI;UAAAjB,QAAA,eACR7J,OAAA,CAACjB,IAAI;YAACgM,UAAU;YAAC7I,SAAS,EAAEA,SAAU;YAAC8I,QAAQ,EAAE9G,YAAa;YAAA2F,QAAA,gBAC5D7J,OAAA,CAACf,GAAG;cAAA4K,QAAA,eACF7J,OAAA,CAACd,GAAG;gBAAC+L,EAAE,EAAE,CAAE;gBAAApB,QAAA,eACT7J,OAAA,CAACjB,IAAI,CAACmM,KAAK;kBAACP,SAAS,EAAC,MAAM;kBAAAd,QAAA,gBAC1B7J,OAAA,CAACjB,IAAI,CAACoM,KAAK;oBAAAtB,QAAA,eAAC7J,OAAA;sBAAA6J,QAAA,EAAQ;oBAAY;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtDrK,OAAA,CAACjB,IAAI,CAACqM,OAAO;oBACXC,IAAI,EAAC,MAAM;oBACXpH,KAAK,EAAEtD,WAAY;oBACnB2K,QAAQ,EAAGnH,CAAC,IAAKvD,cAAc,CAACuD,CAAC,CAACoH,MAAM,CAACtH,KAAK,CAAE;oBAChDuH,WAAW,EAAC,oBAAoB;oBAChCC,QAAQ;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACFrK,OAAA,CAACjB,IAAI,CAACqM,OAAO,CAACM,QAAQ;oBAACL,IAAI,EAAC,SAAS;oBAAAxB,QAAA,EAAC;kBAEtC;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrK,OAAA,CAACf,GAAG;cAAA4K,QAAA,gBACF7J,OAAA,CAACd,GAAG;gBAAC+L,EAAE,EAAE,CAAE;gBAAApB,QAAA,eACT7J,OAAA,CAACjB,IAAI,CAACmM,KAAK;kBAACP,SAAS,EAAC,MAAM;kBAAAd,QAAA,gBAC1B7J,OAAA,CAACjB,IAAI,CAACoM,KAAK;oBAAAtB,QAAA,eAAC7J,OAAA;sBAAA6J,QAAA,EAAQ;oBAAY;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtDrK,OAAA,CAACjB,IAAI,CAACqM,OAAO;oBACXC,IAAI,EAAC,MAAM;oBACXpH,KAAK,EAAE1D,WAAY;oBACnB+K,QAAQ,EAAGnH,CAAC,IAAK3D,cAAc,CAAC2D,CAAC,CAACoH,MAAM,CAACtH,KAAK,CAAE;oBAChDwH,QAAQ;kBAAA;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACFrK,OAAA,CAACjB,IAAI,CAACqM,OAAO,CAACM,QAAQ;oBAACL,IAAI,EAAC,SAAS;oBAAAxB,QAAA,EAAC;kBAEtC;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAuB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNrK,OAAA,CAACd,GAAG;gBAAC+L,EAAE,EAAE,CAAE;gBAAApB,QAAA,eACT7J,OAAA,CAACjB,IAAI,CAACmM,KAAK;kBAACP,SAAS,EAAC,MAAM;kBAAAd,QAAA,gBAC1B7J,OAAA,CAACjB,IAAI,CAACoM,KAAK;oBAAAtB,QAAA,eAAC7J,OAAA;sBAAA6J,QAAA,EAAQ;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrDrK,OAAA,CAACjB,IAAI,CAACqM,OAAO;oBACXO,EAAE,EAAC,UAAU;oBACbC,IAAI,EAAE,CAAE;oBACR3H,KAAK,EAAExD,WAAY;oBACnB6K,QAAQ,EAAGnH,CAAC,IAAKzD,cAAc,CAACyD,CAAC,CAACoH,MAAM,CAACtH,KAAK,CAAE;oBAChDuH,WAAW,EAAC;kBAA4C;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrK,OAAA;cAAK2K,SAAS,EAAC,gFAAgF;cAAAd,QAAA,gBAC7F7J,OAAA;gBAAI2K,SAAS,EAAC,MAAM;gBAAAd,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCrK,OAAA,CAAChB,MAAM;gBACLkG,OAAO,EAAC,SAAS;gBACjB0F,IAAI,EAAC,IAAI;gBACTiB,OAAO,EAAE3I,YAAa;gBACtB4I,QAAQ,EAAElK,YAAa;gBAAAiI,QAAA,gBAEvB7J,OAAA,CAACP,MAAM;kBAACkL,SAAS,EAAC;gBAAM;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,uBAC7B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELxJ,OAAO,CAAC8D,GAAG,CAAC,CAACd,MAAM,EAAET,KAAK,KAAK;cAAA,IAAA2I,qBAAA,EAAAC,sBAAA;cAC9B;cACA,MAAMC,cAAc,GAAG5L,iBAAiB,CAAC8E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKxB,MAAM,CAAC9C,cAAc,CAAC;cAClF,MAAMmL,YAAY,GAAGD,cAAc,GAC/B;gBACEhI,KAAK,EAAEgI,cAAc,CAAC5G,EAAE;gBACxBiF,KAAK,EAAE,GAAG,EAAAyB,qBAAA,GAAAE,cAAc,CAAC7F,sBAAsB,cAAA2F,qBAAA,uBAArCA,qBAAA,CAAuC1F,WAAW,KAAI,SAAS,MAAM4F,cAAc,CAACxG,UAAU,IAAIwG,cAAc,CAACvG,KAAK,KAAKuG,cAAc,CAAC1G,cAAc,IAAI0G,cAAc,CAACzG,UAAU,mBAAmB;gBAClNE,KAAK,EAAEuG,cAAc,CAACvG,KAAK;gBAC3BH,cAAc,EAAE0G,cAAc,CAAC1G,cAAc,IAAI0G,cAAc,CAACzG,UAAU;gBAC1EA,UAAU,EAAEyG,cAAc,CAACzG,UAAU;gBACrCa,WAAW,EAAE,EAAA2F,sBAAA,GAAAC,cAAc,CAAC7F,sBAAsB,cAAA4F,sBAAA,uBAArCA,sBAAA,CAAuC3F,WAAW,KAAI;cACrE,CAAC,GACD,IAAI;;cAER;cACA,MAAM8F,cAAc,GAAG9L,iBAAiB,CAACsE,GAAG,CAAEO,OAAO,IAAK;gBAAA,IAAAkH,sBAAA,EAAAC,sBAAA;gBACxD;gBACA,MAAMC,iBAAiB,GAAG7I,wBAAwB,CAACyB,OAAO,CAACG,EAAE,EAAEjC,KAAK,CAAC;gBAErE,OAAO;kBACLa,KAAK,EAAEiB,OAAO,CAACG,EAAE;kBACjBiF,KAAK,EAAE,GAAG,EAAA8B,sBAAA,GAAAlH,OAAO,CAACkB,sBAAsB,cAAAgG,sBAAA,uBAA9BA,sBAAA,CAAgC/F,WAAW,KAAI,SAAS,MAAMnB,OAAO,CAACO,UAAU,IAAIP,OAAO,CAACQ,KAAK,KAAKR,OAAO,CAACK,cAAc,IAAIL,OAAO,CAACM,UAAU,oBAAoB8G,iBAAiB,GAAG,qBAAqB,GAAG,EAAE,EAAE;kBAChO5G,KAAK,EAAER,OAAO,CAACQ,KAAK;kBACpBH,cAAc,EAAEL,OAAO,CAACK,cAAc,IAAIL,OAAO,CAACM,UAAU;kBAC5DA,UAAU,EAAEN,OAAO,CAACM,UAAU;kBAC9Ba,WAAW,EAAE,EAAAgG,sBAAA,GAAAnH,OAAO,CAACkB,sBAAsB,cAAAiG,sBAAA,uBAA9BA,sBAAA,CAAgChG,WAAW,KAAI,SAAS;kBACrEkG,UAAU,EAAED,iBAAiB,CAAC;gBAChC,CAAC;cACH,CAAC,CAAC;cAEF,oBACEtM,OAAA,CAAClB,IAAI;gBAAa6L,SAAS,EAAC,aAAa;gBAAAd,QAAA,gBACvC7J,OAAA,CAAClB,IAAI,CAAC0N,MAAM;kBAAC7B,SAAS,EAAC,4DAA4D;kBAAAd,QAAA,gBACjF7J,OAAA;oBAAI2K,SAAS,EAAC,MAAM;oBAAAd,QAAA,GAAC,UAAQ,EAACzG,KAAK,GAAG,CAAC;kBAAA;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC7CrK,OAAA,CAAChB,MAAM;oBACLkG,OAAO,EAAC,gBAAgB;oBACxB0F,IAAI,EAAC,IAAI;oBACTiB,OAAO,EAAEA,CAAA,KAAM1I,eAAe,CAACC,KAAK,CAAE;oBACtC0I,QAAQ,EAAEjL,OAAO,CAACkE,MAAM,KAAK,CAAE;oBAAA8E,QAAA,gBAE/B7J,OAAA,CAACN,OAAO;sBAACiL,SAAS,EAAC;oBAAM;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,WAC9B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACdrK,OAAA,CAAClB,IAAI,CAACgM,IAAI;kBAAAjB,QAAA,gBACR7J,OAAA,CAACf,GAAG;oBAAA4K,QAAA,gBACF7J,OAAA,CAACd,GAAG;sBAAC+L,EAAE,EAAE,CAAE;sBAAApB,QAAA,eACT7J,OAAA,CAACjB,IAAI,CAACmM,KAAK;wBAACP,SAAS,EAAC,MAAM;wBAAAd,QAAA,gBAC1B7J,OAAA,CAACjB,IAAI,CAACoM,KAAK;0BAAAtB,QAAA,eAAC7J,OAAA;4BAAA6J,QAAA,EAAQ;0BAAsB;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,EAC/D/I,eAAe,gBACdtB,OAAA;0BAAK2K,SAAS,EAAC,2BAA2B;0BAAAd,QAAA,gBACxC7J,OAAA,CAACb,OAAO;4BAACsN,SAAS,EAAC,QAAQ;4BAAC7B,IAAI,EAAC,IAAI;4BAACD,SAAS,EAAC;0BAAM;4BAAAT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACzDrK,OAAA;4BAAA6J,QAAA,EAAM;0BAAmB;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7B,CAAC,gBAENrK,OAAA,CAAAE,SAAA;0BAAA2J,QAAA,gBACE7J,OAAA,CAACpB,MAAM;4BACL8N,OAAO,EAAEP,cAAe;4BACxBQ,UAAU,EAAE;8BAAEC,MAAM,EAAEvD;4BAAa,CAAE;4BACrCpF,KAAK,EAAEiI,YAAa;4BACpBZ,QAAQ,EAAGuB,cAAc,IAAK;8BAC5B9I,kBAAkB,CAACX,KAAK,EAAE,gBAAgB,EAAEyJ,cAAc,CAAC5I,KAAK,CAAC;4BACnE,CAAE;4BACFuH,WAAW,EAAC,uBAAuB;4BACnCsB,MAAM,EAAE;8BACNC,OAAO,EAAGC,QAAQ,KAAM;gCACtB,GAAGA,QAAQ;gCACXC,WAAW,EAAE,MAAM;gCACnBC,SAAS,EAAE,MAAM;gCACjBpD,MAAM,EAAE,MAAM;gCACd,SAAS,EAAE;kCACTmD,WAAW,EAAE;gCACf;8BACF,CAAC,CAAC;8BACFE,cAAc,EAAGH,QAAQ,KAAM;gCAC7B,GAAGA,QAAQ;gCACXlD,MAAM,EAAE,MAAM;gCACdF,OAAO,EAAE;8BACX,CAAC,CAAC;8BACFwD,MAAM,EAAEA,CAACJ,QAAQ,EAAEK,KAAK,MAAM;gCAC5B,GAAGL,QAAQ;gCACXjD,eAAe,EAAEsD,KAAK,CAACd,UAAU,GAC7B,SAAS,GACTc,KAAK,CAACC,UAAU,GACd,SAAS,GACTD,KAAK,CAACE,SAAS,GACb,SAAS,GACT,OAAO;gCACf7H,KAAK,EAAE2H,KAAK,CAACd,UAAU,GACnB,SAAS,GACTc,KAAK,CAACC,UAAU,GACd,OAAO,GACP,OAAO;gCACbE,MAAM,EAAEH,KAAK,CAACd,UAAU,GAAG,aAAa,GAAG;8BAC7C,CAAC;4BACH;0BAAE;4BAAArC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,EACD,CAACxG,MAAM,CAAC9C,cAAc,IAAImB,SAAS,iBAClClC,OAAA;4BAAK2K,SAAS,EAAC,wBAAwB;4BAAAd,QAAA,EAAC;0BAExC;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CACN;wBAAA,eACD,CACH;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNrK,OAAA,CAACd,GAAG;sBAAC+L,EAAE,EAAE,CAAE;sBAAApB,QAAA,eACT7J,OAAA,CAACjB,IAAI,CAACmM,KAAK;wBAACP,SAAS,EAAC,MAAM;wBAAAd,QAAA,gBAC1B7J,OAAA;0BAAK2K,SAAS,EAAC,wDAAwD;0BAAAd,QAAA,gBACrE7J,OAAA,CAACjB,IAAI,CAACoM,KAAK;4BAACR,SAAS,EAAC,MAAM;4BAAAd,QAAA,eAAC7J,OAAA;8BAAA6J,QAAA,EAAQ;4BAAU;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAQ;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,EACpE4B,cAAc,iBACbjM,OAAA;4BAAM2K,SAAS,EAAErF,UAAU,CAACzB,MAAM,CAAC7C,UAAU,CAAC,IAAIiL,cAAc,CAAC1G,cAAc,IAAI0G,cAAc,CAACzG,UAAU,CAAC,GAAG,mBAAmB,GAAG,oBAAqB;4BAAAqE,QAAA,GAAC,aAC/I,EAACoC,cAAc,CAAC1G,cAAc,IAAI0G,cAAc,CAACzG,UAAU,EAAC,QACzE;0BAAA;4BAAA0E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACP;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNrK,OAAA,CAACjB,IAAI,CAACqM,OAAO;0BACXC,IAAI,EAAC,QAAQ;0BACboC,IAAI,EAAC,MAAM;0BACXC,GAAG,EAAC,GAAG;0BACPzJ,KAAK,EAAEJ,MAAM,CAAC7C,UAAW;0BACzBsK,QAAQ,EAAGnH,CAAC,IAAKJ,kBAAkB,CAACX,KAAK,EAAE,YAAY,EAAEe,CAAC,CAACoH,MAAM,CAACtH,KAAK,CAAE;0BACzEwH,QAAQ;0BACRD,WAAW,EAAC,kBAAkB;0BAC9BmC,SAAS,EAAE1B,cAAc,IAAI3G,UAAU,CAACzB,MAAM,CAAC7C,UAAU,CAAC,IAAIiL,cAAc,CAAC1G,cAAc,IAAI0G,cAAc,CAACzG,UAAU,CAAE;0BAC1HmF,SAAS,EAAEsB,cAAc,IAAI3G,UAAU,CAACzB,MAAM,CAAC7C,UAAU,CAAC,IAAIiL,cAAc,CAAC1G,cAAc,IAAI0G,cAAc,CAACzG,UAAU,CAAC,GAAG,eAAe,GAAG,EAAG;0BACjJiE,KAAK,EAAE;4BAAEK,MAAM,EAAE;0BAAO;wBAAE;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC,eACFrK,OAAA,CAACjB,IAAI,CAACqM,OAAO,CAACM,QAAQ;0BAACL,IAAI,EAAC,SAAS;0BAAAxB,QAAA,EAClCoC,cAAc,IAAI3G,UAAU,CAACzB,MAAM,CAAC7C,UAAU,CAAC,IAAIiL,cAAc,CAAC1G,cAAc,IAAI0G,cAAc,CAACzG,UAAU,CAAC,GAC3G,4BAA4ByG,cAAc,CAAC1G,cAAc,IAAI0G,cAAc,CAACzG,UAAU,mBAAmB,GACzG;wBAAgC;0BAAA0E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrK,OAAA,CAACjB,IAAI,CAACoM,KAAK;oBAACR,SAAS,EAAC,MAAM;oBAAAd,QAAA,eAAC7J,OAAA;sBAAA6J,QAAA,EAAQ;oBAAe;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC1ErK,OAAA,CAACf,GAAG;oBAAA4K,QAAA,GACD,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAClF,GAAG,CAAC,CAACiG,IAAI,EAAEgD,SAAS,KAAK;sBACpD,MAAMC,OAAO,GAAGjD,IAAI,CAACkD,WAAW,CAAC,CAAC;sBAClC,oBACE9N,OAAA,CAACd,GAAG;wBAAiB+B,EAAE,EAAE,CAAE;wBAAC8M,EAAE,EAAE,CAAE;wBAAC9C,EAAE,EAAE,CAAE;wBAACN,SAAS,EAAC,MAAM;wBAAAd,QAAA,eACxD7J,OAAA,CAACjB,IAAI,CAACmM,KAAK;0BAAArB,QAAA,gBACT7J,OAAA,CAACjB,IAAI,CAACoM,KAAK;4BAACR,SAAS,EAAC,qBAAqB;4BAAAd,QAAA,EAAEe;0BAAI;4BAAAV,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAa,CAAC,eAC/DrK,OAAA,CAACjB,IAAI,CAACqM,OAAO;4BACXC,IAAI,EAAC,QAAQ;4BACbqC,GAAG,EAAC,GAAG;4BACPzJ,KAAK,EAAEJ,MAAM,CAACgK,OAAO,CAAE;4BACvBvC,QAAQ,EAAGnH,CAAC,IAAK;8BACf,MAAM6J,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAElF,QAAQ,CAAC7E,CAAC,CAACoH,MAAM,CAACtH,KAAK,IAAI,CAAC,CAAC,CAAC;8BACtDF,kBAAkB,CAACX,KAAK,EAAEyK,OAAO,EAAEG,GAAG,CAAC;4BACzC,CAAE;4BACFrD,SAAS,EAAC;0BAAa;4BAAAT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACQ;sBAAC,GAbLuD,SAAS;wBAAA1D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAcd,CAAC;oBAEV,CAAC,CAAC,eACFrK,OAAA,CAACd,GAAG;sBAAC+B,EAAE,EAAE,CAAE;sBAAC8M,EAAE,EAAE,CAAE;sBAAC9C,EAAE,EAAE,CAAE;sBAACN,SAAS,EAAC,MAAM;sBAAAd,QAAA,eACxC7J,OAAA,CAACjB,IAAI,CAACmM,KAAK;wBAAArB,QAAA,gBACT7J,OAAA,CAACjB,IAAI,CAACoM,KAAK;0BAACR,SAAS,EAAC,qBAAqB;0BAAAd,QAAA,EAAC;wBAAK;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eAC9DrK,OAAA;0BAAK2K,SAAS,EAAC,mCAAmC;0BAAAd,QAAA,EAC/Cb,QAAQ,CAACnF,MAAM,CAAC5C,EAAE,IAAI,CAAC,CAAC,GACxB+H,QAAQ,CAACnF,MAAM,CAAC3C,CAAC,IAAI,CAAC,CAAC,GACvB8H,QAAQ,CAACnF,MAAM,CAAC1C,CAAC,IAAI,CAAC,CAAC,GACvB6H,QAAQ,CAACnF,MAAM,CAACzC,CAAC,IAAI,CAAC,CAAC,GACvB4H,QAAQ,CAACnF,MAAM,CAACxC,EAAE,IAAI,CAAC;wBAAC;0BAAA6I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA,GA5IHjH,KAAK;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6IV,CAAC;YAEX,CAAC,CAAC,eAEFrK,OAAA;cAAK2K,SAAS,EAAC,iCAAiC;cAAAd,QAAA,eAC9C7J,OAAA,CAAClB,IAAI;gBAAC6L,SAAS,EAAC,UAAU;gBAAClB,KAAK,EAAE;kBAAEM,eAAe,EAAE;gBAAU,CAAE;gBAAAF,QAAA,eAC/D7J,OAAA,CAAClB,IAAI,CAACgM,IAAI;kBAACH,SAAS,EAAC,MAAM;kBAAAd,QAAA,eACzB7J,OAAA;oBAAK2K,SAAS,EAAC,oBAAoB;oBAAAd,QAAA,gBACjC7J,OAAA;sBAAK2K,SAAS,EAAC,gCAAgC;sBAAAd,QAAA,gBAC7C7J,OAAA;wBAAQ2K,SAAS,EAAC,MAAM;wBAAAd,QAAA,EAAC;sBAAiB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnDrK,OAAA,CAACV,KAAK;wBAAC6O,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,MAAI,EAAClD,eAAe,CAAC1F,EAAE;sBAAA;wBAAAiJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACrErK,OAAA,CAACV,KAAK;wBAAC6O,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,KAAG,EAAClD,eAAe,CAACzF,CAAC;sBAAA;wBAAAgJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnErK,OAAA,CAACV,KAAK;wBAAC6O,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,KAAG,EAAClD,eAAe,CAACxF,CAAC;sBAAA;wBAAA+I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnErK,OAAA,CAACV,KAAK;wBAAC6O,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,KAAG,EAAClD,eAAe,CAACvF,CAAC;sBAAA;wBAAA8I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnErK,OAAA,CAACV,KAAK;wBAAC6O,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,MAAI,EAAClD,eAAe,CAACtF,EAAE;sBAAA;wBAAA6I,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACrErK,OAAA,CAACV,KAAK;wBAAC6O,EAAE,EAAC,SAAS;wBAACxD,SAAS,EAAC,MAAM;wBAAAd,QAAA,GAAC,SAAO,EAAClD,eAAe,CAACoC,KAAK;sBAAA;wBAAAmB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC,eACNrK,OAAA;sBAAK2K,SAAS,EAAC,2BAA2B;sBAAAd,QAAA,gBACxC7J,OAAA;wBAAQ2K,SAAS,EAAC,MAAM;wBAAAd,QAAA,EAAC;sBAAiB;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACnDrK,OAAA,CAACV,KAAK;wBAAC6O,EAAE,EAAC,MAAM;wBAAAtE,QAAA,GAAElD,eAAe,CAAC3F,UAAU,CAACiI,OAAO,CAAC,CAAC,CAAC,EAAC,QAAM;sBAAA;wBAAAiB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENrK,OAAA;cAAK2K,SAAS,EAAC,oCAAoC;cAAAd,QAAA,eACjD7J,OAAA,CAAChB,MAAM;gBACLqM,IAAI,EAAC,QAAQ;gBACbnG,OAAO,EAAC,SAAS;gBACjB0F,IAAI,EAAC,IAAI;gBACTkB,QAAQ,EAAElK,YAAa;gBACvB+I,SAAS,EAAC,MAAM;gBAAAd,QAAA,EAEfjI,YAAY,gBACX5B,OAAA,CAAAE,SAAA;kBAAA2J,QAAA,gBACE7J,OAAA,CAACb,OAAO;oBAACwM,EAAE,EAAC,MAAM;oBAACc,SAAS,EAAC,QAAQ;oBAAC7B,IAAI,EAAC,IAAI;oBAACwD,IAAI,EAAC,QAAQ;oBAAC,eAAY,MAAM;oBAACzD,SAAS,EAAC;kBAAM;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,iBAEtG;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNrK,OAAA,CAACT,KAAK;MAAC8O,IAAI,EAAEjM,YAAa;MAACkM,MAAM,EAAElF,gBAAiB;MAACwB,IAAI,EAAC,IAAI;MAAC2D,QAAQ;MAAA1E,QAAA,gBACrE7J,OAAA,CAACT,KAAK,CAACiN,MAAM;QAACgC,WAAW;QAAA3E,QAAA,eACvB7J,OAAA,CAACT,KAAK,CAACkP,KAAK;UAAA5E,QAAA,EAAC;QAA2B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eACfrK,OAAA,CAACT,KAAK,CAACuL,IAAI;QAAAjB,QAAA,gBACT7J,OAAA;UAAA6J,QAAA,EAAG;QAAyD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAE/D/H,eAAe,iBACdtC,OAAA;UAAK2K,SAAS,EAAC,MAAM;UAAAd,QAAA,gBACnB7J,OAAA;YAAA6J,QAAA,EAAG;UAA+C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtDrK,OAAA;YAAA6J,QAAA,gBACE7J,OAAA;cAAA6J,QAAA,gBAAI7J,OAAA;gBAAA6J,QAAA,EAAQ;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC/H,eAAe,CAACuD,YAAY;YAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtErK,OAAA;cAAA6J,QAAA,gBAAI7J,OAAA;gBAAA6J,QAAA,EAAQ;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC/H,eAAe,CAAC+D,WAAW;YAAA;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/DrK,OAAA;cAAA6J,QAAA,gBAAI7J,OAAA;gBAAA6J,QAAA,EAAQ;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,IAAIxC,IAAI,CAACvF,eAAe,CAACsD,YAAY,CAAC,CAACkC,kBAAkB,CAAC,CAAC;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrGrK,OAAA;cAAA6J,QAAA,gBAAI7J,OAAA;gBAAA6J,QAAA,EAAQ;cAAiB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,SAAK,EAAC/H,eAAe,CAACqE,eAAe,CAAC1F,EAAE,EAAC,OAC1E,EAACqB,eAAe,CAACqE,eAAe,CAACzF,CAAC,EAAC,OACnC,EAACoB,eAAe,CAACqE,eAAe,CAACxF,CAAC,EAAC,OACnC,EAACmB,eAAe,CAACqE,eAAe,CAACvF,CAAC,EAAC,QAClC,EAACkB,eAAe,CAACqE,eAAe,CAACtF,EAAE;YAAA;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/CrK,OAAA;cAAA6J,QAAA,gBAAI7J,OAAA;gBAAA6J,QAAA,EAAQ;cAAY;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC/H,eAAe,CAACqE,eAAe,CAACoC,KAAK;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9ErK,OAAA;cAAA6J,QAAA,gBAAI7J,OAAA;gBAAA6J,QAAA,EAAQ;cAAiB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC/H,eAAe,CAACqE,eAAe,CAAC3F,UAAU,CAACiI,OAAO,CAAC,CAAC,CAAC,EAAC,QAAM;YAAA;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACbrK,OAAA,CAACT,KAAK,CAACmP,MAAM;QAAA7E,QAAA,gBACX7J,OAAA,CAAChB,MAAM;UAACkG,OAAO,EAAC,WAAW;UAAC2G,OAAO,EAAEzC,gBAAiB;UAAAS,QAAA,EAAC;QAEvD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrK,OAAA,CAAChB,MAAM;UAACkG,OAAO,EAAC,SAAS;UAAC2G,OAAO,EAAEhF,WAAY;UAAAgD,QAAA,gBAC7C7J,OAAA,CAACH,SAAS;YAAC8K,SAAS,EAAC;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAChC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAACjK,EAAA,CAluBID,gBAAgB;AAAAwO,EAAA,GAAhBxO,gBAAgB;AAouBtB,eAAeA,gBAAgB;AAAC,IAAAwO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}